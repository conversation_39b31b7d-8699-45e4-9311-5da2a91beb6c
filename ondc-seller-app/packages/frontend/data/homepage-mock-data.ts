// Comprehensive mock data for homepage components with tenant isolation

export interface HeroBannerSlide {
  id: string;
  title: string;
  subtitle: string;
  description: string;
  image: string;
  mobileImage?: string;
  tabletImage?: string;
  buttonText: string;
  buttonLink: string;
  backgroundColor: string;
  textColor?: string;
  isActive: boolean;
  position: number;
  tenantId: string;
}

export interface FeaturedProduct {
  id: string;
  name: string;
  description: string;
  price: number;
  originalPrice?: number;
  image: string;
  images: string[];
  rating: number;
  reviewCount: number;
  category: string;
  brand: string;
  isInStock: boolean;
  stockCount: number;
  badge?: string;
  tenantId: string;
  slug:string;
}

export interface CategoryShowcase {
  id: string;
  name: string;
  description: string;
  image: string;
  productCount: number;
  slug: string;
  isActive: boolean;
  tenantId: string;
}

export interface PromotionalBanner {
  id: string;
  title: string;
  subtitle: string;
  description: string;
  image: string;
  discountPercentage?: number;
  validUntil: string;
  buttonText: string;
  buttonLink: string;
  backgroundColor: string;
  isActive: boolean;
  tenantId: string;
}

export interface BrandShowcase {
  id: string;
  name: string;
  logo: string;
  description: string;
  productCount: number;
  isPartner: boolean;
  website?: string;
  tenantId: string;
}

export interface CustomerTestimonial {
  id: string;
  customerName: string;
  customerAvatar: string;
  rating: number;
  review: string;
  productName?: string;
  location: string;
  date: string;
  isVerified: boolean;
  tenantId: string;
}

export interface NewsletterCTA {
  id: string;
  title: string;
  subtitle: string;
  description: string;
  backgroundImage: string;
  buttonText: string;
  placeholderText: string;
  benefits: string[];
  tenantId: string;
}

// Default tenant mock data
export const defaultTenantId = '20';

export const heroBannerSlides: HeroBannerSlide[] = [
  {
    id: 'banner-1',
    title: 'Summer Electronics Sale',
    subtitle: 'Up to 70% Off',
    description: 'Discover amazing deals on the latest smartphones, laptops, and smart home devices',
    image: '/images/banners/summer-electronics-sale.svg',
    mobileImage: '/images/banners/mobile/summer-electronics-sale.svg',
    tabletImage: '/images/banners/summer-electronics-sale.svg',
    buttonText: 'Shop Electronics',
    buttonLink: '/categories/electronics',
    backgroundColor: 'bg-gradient-to-r from-blue-600 to-purple-600',
    textColor: 'text-white',
    isActive: true,
    position: 1,
    tenantId: defaultTenantId,
  },
  {
    id: 'banner-2',
    title: 'Fashion Forward Collection',
    subtitle: 'New Arrivals',
    description: 'Explore the latest trends in fashion with our curated collection of premium brands',
    image: '/images/banners/fashion-collection.svg',
    mobileImage: '/images/banners/mobile/fashion-collection.svg',
    tabletImage: '/images/banners/fashion-collection.svg',
    buttonText: 'Explore Fashion',
    buttonLink: '/categories/fashion',
    backgroundColor: 'bg-gradient-to-r from-pink-500 to-rose-600',
    textColor: 'text-white',
    isActive: true,
    position: 2,
    tenantId: defaultTenantId,
  },
  {
    id: 'banner-3',
    title: 'Home & Garden Paradise',
    subtitle: 'Transform Your Space',
    description: 'Beautiful furniture, decor, and garden essentials to create your perfect home',
    image: '/images/banners/home-garden-paradise.svg',
    mobileImage: '/images/banners/mobile/home-garden-paradise.svg',
    tabletImage: '/images/banners/home-garden-paradise.svg',
    buttonText: 'Shop Home & Garden',
    buttonLink: '/categories/home-garden',
    backgroundColor: 'bg-gradient-to-r from-green-500 to-emerald-600',
    textColor: 'text-white',
    isActive: true,
    position: 3,
    tenantId: defaultTenantId,
  },
  {
    id: 'banner-4',
    title: 'Fitness & Wellness',
    subtitle: 'Your Health Journey',
    description: 'Premium fitness equipment, supplements, and wellness products for a healthier you',
    image: '/images/banners/fitness-wellness.svg',
    mobileImage: '/images/banners/mobile/fitness-wellness.svg',
    tabletImage: '/images/banners/fitness-wellness.svg',
    buttonText: 'Shop Fitness',
    buttonLink: '/categories/sports-fitness',
    backgroundColor: 'bg-gradient-to-r from-orange-500 to-red-600',
    textColor: 'text-white',
    isActive: true,
    position: 4,
    tenantId: defaultTenantId,
  },
];

export const featuredProducts: FeaturedProduct[] = [

  // TechHub Electronics (tenant ID: 20) - Tech focused featured products
  {
    id: 'fashion-featured-1',
    name: 'Designer Leather Handbag',
    slug: 'designer-leather-handbag',
    description: 'Premium leather handbag with elegant design',
    price: 12999,
    originalPrice: 18999,
    images: [
      '/images/products/handbag.jpg',
      '/images/products/handbag-2.jpg',
    ],
    image: '/images/products/handbag.jpg',
    rating: 4.8,
    reviewCount: 298,
    category: 'Fashion',
    brand: 'LuxeStyle',
    isInStock: true,
    stockCount: 34,
    badge: 'Sale',
    tenantId: '19',
  },
  {
    id: 'fashion-featured-2',
    name: 'Premium Running Shoes',
    slug: 'premium-running-shoes',
    description: 'Comfortable running shoes with advanced cushioning',
    price: 9999,
    originalPrice: 14999,
    images: [
      '/images/products/running-shoes.jpg',
      '/images/products/running-shoes-2.jpg',
    ],
    image: '/images/products/running-shoes.jpg',
    rating: 4.6,
    reviewCount: 445,
    category: 'Fashion',
    brand: 'SportStyle',
    isInStock: true,
    stockCount: 56,
    badge: 'Sale',
    tenantId: '19',
  },
  {
    id: 'fashion-featured-3',
    name: 'Luxury Watch Collection',
    slug: 'luxury-watch-collection',
    description: 'Elegant timepiece with premium materials',
    price: 54999,
    originalPrice: 79999,
    images: [
      '/images/products/luxury-watch.jpg',
      '/images/products/luxury-watch-2.jpg',
    ],
    image: '/images/products/luxury-watch.jpg',
    rating: 4.9,
    reviewCount: 187,
    category: 'Fashion',
    brand: 'TimeStyle',
    isInStock: true,
    stockCount: 12,
    badge: 'Sale',
    tenantId: '19',
  },
  {
    id: 'fashion-featured-4',
    name: 'Cashmere Winter Scarf',
    slug: 'cashmere-winter-scarf',
    description: 'Soft cashmere scarf perfect for winter',
    price: 5999,
    originalPrice: 8999,
    images: [
      '/images/products/scarf.jpg',
      '/images/products/scarf-2.jpg',
    ],
    image: '/images/products/scarf.jpg',
    rating: 4.4,
    reviewCount: 123,
    category: 'Fashion',
    brand: 'WarmStyle',
    isInStock: true,
    stockCount: 78,
    badge: 'Sale',
    tenantId: '19',
  },
  {
    id: 'fashion-featured-5',
    name: 'Designer Denim Jacket',
    slug: 'designer-denim-jacket',
    description: 'Vintage-style denim jacket with premium stitching',
    price: 7999,
    originalPrice: 11999,
    images: [
      '/images/products/denim-jacket.jpg',
      '/images/products/denim-jacket-2.jpg',
    ],
    image: '/images/products/denim-jacket.jpg',
    rating: 4.5,
    reviewCount: 189,
    category: 'Fashion',
    brand: 'DenimStyle',
    isInStock: true,
    stockCount: 34,
    badge: 'Trending',
    tenantId: '19',
  },
  {
    id: 'fashion-featured-6',
    name: 'Silk Evening Dress',
    slug: 'silk-evening-dress',
    description: 'Elegant silk evening dress for special occasions',
    price: 23999,
    originalPrice: 34999,
    images: [
      '/images/products/evening-dress.jpg',
      '/images/products/evening-dress-2.jpg',
    ],
    image: '/images/products/evening-dress.jpg',
    rating: 4.8,
    reviewCount: 145,
    category: 'Fashion',
    brand: 'ElegantStyle',
    isInStock: true,
    stockCount: 12,
    badge: 'Luxury',
    tenantId: '19',
  },
  {
    id: 'fashion-featured-7',
    name: 'Premium Leather Boots',
    slug: 'premium-leather-boots',
    description: 'Handcrafted leather boots with waterproof coating',
    price: 15999,
    originalPrice: 22999,
    images: [
      '/images/products/leather-boots.jpg',
      '/images/products/leather-boots-2.jpg',
    ],
    image: '/images/products/leather-boots.jpg',
    rating: 4.7,
    reviewCount: 267,
    category: 'Fashion',
    brand: 'BootCraft',
    isInStock: true,
    stockCount: 45,
    badge: 'Sale',
    tenantId: '19',
  },
  {
    id: 'fashion-featured-8',
    name: 'Designer Sunglasses',
    slug: 'designer-sunglasses',
    description: 'UV protection designer sunglasses with polarized lenses',
    price: 13999,
    originalPrice: 19999,
    images: [
      '/images/products/sunglasses.jpg',
      '/images/products/sunglasses-2.jpg',
    ],
    image: '/images/products/sunglasses.jpg',
    rating: 4.6,
    reviewCount: 198,
    category: 'Fashion',
    brand: 'SunStyle',
    isInStock: true,
    stockCount: 67,
    badge: 'Sale',
    tenantId: '19',
  },
  // StyleHub Fashion (tenant ID: 19) - Fashion focused featured products
  {
    id: 'fashion-featured-1',
    name: 'Designer Leather Handbag',
    slug: 'designer-leather-handbag',
    description: 'Premium leather handbag with elegant design',
    price: 12999,
    originalPrice: 18999,
    images: [
      '/images/products/handbag.jpg',
      '/images/products/handbag-2.jpg',
    ],
    image: '/images/products/handbag.jpg',
    rating: 4.8,
    reviewCount: 298,
    category: 'Fashion',
    brand: 'LuxeStyle',
    isInStock: true,
    stockCount: 34,
    badge: 'Sale',
    tenantId: '19',
  },
  {
    id: 'fashion-featured-2',
    name: 'Premium Running Shoes',
    slug: 'premium-running-shoes',
    description: 'Comfortable running shoes with advanced cushioning',
    price: 9999,
    originalPrice: 14999,
    images: [
      '/images/products/running-shoes.jpg',
      '/images/products/running-shoes-2.jpg',
    ],
    image: '/images/products/running-shoes.jpg',
    rating: 4.6,
    reviewCount: 445,
    category: 'Fashion',
    brand: 'SportStyle',
    isInStock: true,
    stockCount: 56,
    badge: 'Sale',
    tenantId: '19',
  },
  {
    id: 'fashion-featured-3',
    name: 'Luxury Watch Collection',
    slug: 'luxury-watch-collection',
    description: 'Elegant timepiece with premium materials',
    price: 54999,
    originalPrice: 79999,
    images: [
      '/images/products/luxury-watch.jpg',
      '/images/products/luxury-watch-2.jpg',
    ],
    image: '/images/products/luxury-watch.jpg',
    rating: 4.9,
    reviewCount: 187,
    category: 'Fashion',
    brand: 'TimeStyle',
    isInStock: true,
    stockCount: 12,
    badge: 'Sale',
    tenantId: '19',
  },
  {
    id: 'fashion-featured-4',
    name: 'Cashmere Winter Scarf',
    slug: 'cashmere-winter-scarf',
    description: 'Soft cashmere scarf perfect for winter',
    price: 5999,
    originalPrice: 8999,
    images: [
      '/images/products/scarf.jpg',
      '/images/products/scarf-2.jpg',
    ],
    image: '/images/products/scarf.jpg',
    rating: 4.4,
    reviewCount: 123,
    category: 'Fashion',
    brand: 'WarmStyle',
    isInStock: true,
    stockCount: 78,
    badge: 'Sale',
    tenantId: '19',
  },
  {
    id: 'fashion-featured-5',
    name: 'Designer Denim Jacket',
    slug: 'designer-denim-jacket',
    description: 'Vintage-style denim jacket with premium stitching',
    price: 7999,
    originalPrice: 11999,
    images: [
      '/images/products/denim-jacket.jpg',
      '/images/products/denim-jacket-2.jpg',
    ],
    image: '/images/products/denim-jacket.jpg',
    rating: 4.5,
    reviewCount: 189,
    category: 'Fashion',
    brand: 'DenimStyle',
    isInStock: true,
    stockCount: 34,
    badge: 'Trending',
    tenantId: '19',
  },
  {
    id: 'fashion-featured-6',
    name: 'Silk Evening Dress',
    slug: 'silk-evening-dress',
    description: 'Elegant silk evening dress for special occasions',
    price: 23999,
    originalPrice: 34999,
    images: [
      '/images/products/evening-dress.jpg',
      '/images/products/evening-dress-2.jpg',
    ],
    image: '/images/products/evening-dress.jpg',
    rating: 4.8,
    reviewCount: 145,
    category: 'Fashion',
    brand: 'ElegantStyle',
    isInStock: true,
    stockCount: 12,
    badge: 'Luxury',
    tenantId: '19',
  },
  {
    id: 'fashion-featured-7',
    name: 'Premium Leather Boots',
    slug: 'premium-leather-boots',
    description: 'Handcrafted leather boots with waterproof coating',
    price: 15999,
    originalPrice: 22999,
    images: [
      '/images/products/leather-boots.jpg',
      '/images/products/leather-boots-2.jpg',
    ],
    image: '/images/products/leather-boots.jpg',
    rating: 4.7,
    reviewCount: 267,
    category: 'Fashion',
    brand: 'BootCraft',
    isInStock: true,
    stockCount: 45,
    badge: 'Sale',
    tenantId: '19',
  },
  {
    id: 'fashion-featured-8',
    name: 'Designer Sunglasses',
    slug: 'designer-sunglasses',
    description: 'UV protection designer sunglasses with polarized lenses',
    price: 13999,
    originalPrice: 19999,
    images: [
      '/images/products/sunglasses.jpg',
      '/images/products/sunglasses-2.jpg',
    ],
    image: '/images/products/sunglasses.jpg',
    rating: 4.6,
    reviewCount: 198,
    category: 'Fashion',
    brand: 'SunStyle',
    isInStock: true,
    stockCount: 67,
    badge: 'Sale',
    tenantId: '19',
  },
  // HomeHub Essentials (tenant ID: 21) - Home focused featured products
  {
    id: 'home-featured-1',
    name: 'Ergonomic Office Chair',
    slug: 'ergonomic-office-chair',
    description: 'Premium ergonomic office chair with lumbar support',
    price: 19999,
    originalPrice: 29999,
    images: [
      '/images/products/chair.jpg',
      '/images/products/chair-2.jpg',
    ],
    image: '/images/products/chair.jpg',
    rating: 4.6,
    reviewCount: 167,
    category: 'Home & Garden',
    brand: 'ComfortPlus',
    isInStock: true,
    stockCount: 23,
    badge: 'Sale',
    tenantId: '21',
  },
  {
    id: 'home-featured-2',
    name: 'LED Desk Lamp with USB',
    slug: 'led-desk-lamp-with-usb',
    description: 'Modern LED desk lamp with USB charging port',
    price: 3499,
    originalPrice: 4999,
    images: [
      '/images/products/lamp.jpg',
      '/images/products/lamp-2.jpg',
    ],
    image: '/images/products/lamp.jpg',
    rating: 4.3,
    reviewCount: 89,
    category: 'Home & Garden',
    brand: 'LightTech',
    isInStock: true,
    stockCount: 45,
    badge: 'Sale',
    tenantId: '21',
  },
  {
    id: 'home-featured-3',
    name: 'Professional Blender Pro',
    slug: 'professional-blender-pro',
    description: 'High-performance blender for smoothies and cooking',
    price: 13999,
    originalPrice: 19999,
    images: [
      '/images/products/blender.jpg',
      '/images/products/blender-2.jpg',
    ],
    image: '/images/products/blender.jpg',
    rating: 4.7,
    reviewCount: 267,
    category: 'Home & Garden',
    brand: 'KitchenPro',
    isInStock: true,
    stockCount: 34,
    badge: 'Sale',
    tenantId: '21',
  },
  {
    id: 'home-featured-4',
    name: 'Stainless Steel Cookware Set',
    slug: 'stainless-steel-cookware-set',
    description: 'Complete cookware set with non-stick coating',
    price: 19999,
    originalPrice: 29999,
    images: [
      '/images/products/cookware-set.jpg',
      '/images/products/cookware-set-2.jpg',
    ],
    image: '/images/products/cookware-set.jpg',
    rating: 4.8,
    reviewCount: 189,
    category: 'Home & Garden',
    brand: 'CookMaster',
    isInStock: true,
    stockCount: 18,
    badge: 'Sale',
    tenantId: '21',
  },
  {
    id: 'home-featured-5',
    name: 'Smart Air Purifier',
    slug: 'smart-air-purifier',
    description: 'HEPA filter air purifier with app control and air quality monitoring',
    price: 24999,
    originalPrice: 32999,
    images: [
      '/images/products/air-purifier.jpg',
      '/images/products/air-purifier-2.jpg',
    ],
    image: '/images/products/air-purifier.jpg',
    rating: 4.7,
    reviewCount: 234,
    category: 'Home & Garden',
    brand: 'AirTech',
    isInStock: true,
    stockCount: 28,
    badge: 'Smart',
    tenantId: '21',
  },
  {
    id: 'home-featured-6',
    name: 'Memory Foam Mattress',
    slug: 'memory-foam-mattress',
    description: 'Queen size memory foam mattress with cooling gel layer',
    price: 39999,
    originalPrice: 59999,
    images: [
      '/images/products/mattress.jpg',
      '/images/products/mattress-2.jpg',
    ],
    image: '/images/products/mattress.jpg',
    rating: 4.8,
    reviewCount: 156,
    category: 'Home & Garden',
    brand: 'SleepWell',
    isInStock: true,
    stockCount: 8,
    badge: 'Comfort',
    tenantId: '21',
  },
  {
    id: 'home-featured-7',
    name: 'Modern Coffee Table',
    slug: 'modern-coffee-table',
    description: 'Minimalist coffee table with hidden storage compartment',
    price: 17999,
    originalPrice: 24999,
    images: [
      '/images/products/coffee-table.jpg',
      '/images/products/coffee-table-2.jpg',
    ],
    image: '/images/products/coffee-table.jpg',
    rating: 4.6,
    reviewCount: 123,
    category: 'Home & Garden',
    brand: 'ModernHome',
    isInStock: true,
    stockCount: 15,
    badge: 'Sale',
    tenantId: '21',
  },
  {
    id: 'home-featured-8',
    name: 'Robot Vacuum Cleaner',
    slug: 'robot-vacuum-cleaner',
    description: 'Smart robot vacuum with mapping and app control',
    price: 32999,
    originalPrice: 44999,
    images: [
      '/images/products/robot-vacuum.jpg',
      '/images/products/robot-vacuum-2.jpg',
    ],
    image: '/images/products/robot-vacuum.jpg',
    rating: 4.5,
    reviewCount: 289,
    category: 'Home & Garden',
    brand: 'CleanBot',
    isInStock: true,
    stockCount: 22,
    badge: 'Smart',
    tenantId: '21',
  },
];

export const categoryShowcase: CategoryShowcase[] = [
  // Default tenant categories
  {
    id: 'cat-1',
    name: 'Electronics',
    description: 'Latest smartphones, laptops, and gadgets',
    image: '/images/categories/electronics-showcase.jpg',
    productCount: 1247,
    slug: 'electronics',
    isActive: true,
    tenantId: defaultTenantId,
  },
  {
    id: 'cat-2',
    name: 'Fashion',
    description: 'Trendy clothing and accessories',
    image: '/images/categories/fashion-showcase.jpg',
    productCount: 892,
    slug: 'fashion',
    isActive: true,
    tenantId: defaultTenantId,
  },
  {
    id: 'cat-3',
    name: 'Home & Garden',
    description: 'Furniture, decor, and garden essentials',
    image: '/images/categories/home-garden-showcase.jpg',
    productCount: 634,
    slug: 'home-garden',
    isActive: true,
    tenantId: defaultTenantId,
  },
  {
    id: 'cat-4',
    name: 'Sports & Fitness',
    description: 'Equipment and gear for active lifestyle',
    image: '/images/categories/sports-fitness-showcase.jpg',
    productCount: 456,
    slug: 'sports-fitness',
    isActive: true,
    tenantId: defaultTenantId,
  },
  {
    id: 'cat-5',
    name: 'Beauty & Health',
    description: 'Skincare, cosmetics, and wellness products',
    image: '/images/categories/beauty-health-showcase.jpg',
    productCount: 723,
    slug: 'beauty-health',
    isActive: true,
    tenantId: defaultTenantId,
  },
  {
    id: 'cat-6',
    name: 'Books & Media',
    description: 'Books, movies, music, and educational content',
    image: '/images/categories/books-media-showcase.jpg',
    productCount: 389,
    slug: 'books-media',
    isActive: true,
    tenantId: defaultTenantId,
  },

  // TechHub Electronics (tenant ID: 4) - Electronics focused categories
  {
    id: 'cat-tech-1',
    name: 'Electronics',
    description: 'Cutting-edge smartphones, laptops, and tech gadgets',
    image: '/images/categories/electronics-showcase.jpg',
    productCount: 1847,
    slug: 'electronics',
    isActive: true,
    tenantId: '4',
  },
  {
    id: 'cat-tech-2',
    name: 'Gaming',
    description: 'Gaming consoles, accessories, and equipment',
    image: '/images/categories/gaming-showcase.jpg',
    productCount: 567,
    slug: 'gaming',
    isActive: true,
    tenantId: '4',
  },
  {
    id: 'cat-tech-3',
    name: 'Smart Home',
    description: 'IoT devices and smart home automation',
    image: '/images/categories/smart-home-showcase.jpg',
    productCount: 423,
    slug: 'smart-home',
    isActive: true,
    tenantId: '4',
  },
  {
    id: 'cat-tech-4',
    name: 'Audio & Video',
    description: 'Headphones, speakers, and entertainment systems',
    image: '/images/categories/audio-video-showcase.jpg',
    productCount: 789,
    slug: 'audio-video',
    isActive: true,
    tenantId: '4',
  },
  {
    id: 'cat-tech-5',
    name: 'Accessories',
    description: 'Cables, cases, chargers, and tech accessories',
    image: '/images/categories/accessories-showcase.jpg',
    productCount: 1234,
    slug: 'accessories',
    isActive: true,
    tenantId: '4',
  },

  // StyleHub Fashion (tenant ID: 5) - Fashion focused categories
  {
    id: 'cat-style-1',
    name: 'Women\'s Fashion',
    description: 'Trendy clothing and accessories for women',
    image: '/images/categories/womens-fashion-showcase.jpg',
    productCount: 1456,
    slug: 'womens-fashion',
    isActive: true,
    tenantId: '5',
  },
  {
    id: 'cat-style-2',
    name: 'Men\'s Fashion',
    description: 'Stylish clothing and accessories for men',
    image: '/images/categories/mens-fashion-showcase.jpg',
    productCount: 987,
    slug: 'mens-fashion',
    isActive: true,
    tenantId: '5',
  },
  {
    id: 'cat-style-3',
    name: 'Footwear',
    description: 'Shoes, sneakers, and boots for all occasions',
    image: '/images/categories/footwear-showcase.jpg',
    productCount: 678,
    slug: 'footwear',
    isActive: true,
    tenantId: '5',
  },
  {
    id: 'cat-style-4',
    name: 'Accessories',
    description: 'Bags, jewelry, watches, and fashion accessories',
    image: '/images/categories/fashion-accessories-showcase.jpg',
    productCount: 543,
    slug: 'accessories',
    isActive: true,
    tenantId: '5',
  },
  {
    id: 'cat-style-5',
    name: 'Beauty & Cosmetics',
    description: 'Makeup, skincare, and beauty products',
    image: '/images/categories/beauty-cosmetics-showcase.jpg',
    productCount: 892,
    slug: 'beauty-cosmetics',
    isActive: true,
    tenantId: '5',
  },

  // HomeHub Essentials (tenant ID: 6) - Home focused categories
  {
    id: 'cat-home-1',
    name: 'Furniture',
    description: 'Quality furniture for every room',
    image: '/images/categories/furniture-showcase.jpg',
    productCount: 756,
    slug: 'furniture',
    isActive: true,
    tenantId: '6',
  },
  {
    id: 'cat-home-2',
    name: 'Home Decor',
    description: 'Decorative items and home styling accessories',
    image: '/images/categories/home-decor-showcase.jpg',
    productCount: 1123,
    slug: 'home-decor',
    isActive: true,
    tenantId: '6',
  },
  {
    id: 'cat-home-3',
    name: 'Kitchen & Dining',
    description: 'Cookware, appliances, and dining essentials',
    image: '/images/categories/kitchen-dining-showcase.jpg',
    productCount: 634,
    slug: 'kitchen-dining',
    isActive: true,
    tenantId: '6',
  },
  {
    id: 'cat-home-4',
    name: 'Garden & Outdoor',
    description: 'Gardening tools, plants, and outdoor furniture',
    image: '/images/categories/garden-outdoor-showcase.jpg',
    productCount: 445,
    slug: 'garden-outdoor',
    isActive: true,
    tenantId: '6',
  },
  {
    id: 'cat-home-5',
    name: 'Storage & Organization',
    description: 'Storage solutions and organization systems',
    image: '/images/categories/storage-organization-showcase.jpg',
    productCount: 367,
    slug: 'storage-organization',
    isActive: true,
    tenantId: '6',
  },
];

// Helper functions for tenant-specific data
export const getHeroBannerSlidesByTenant = (tenantId: string = defaultTenantId): HeroBannerSlide[] => {
  return heroBannerSlides.filter(slide => slide.tenantId === tenantId && slide.isActive);
};

export const getFeaturedProductsByTenant = (tenantId: string = defaultTenantId): FeaturedProduct[] => {
  return featuredProducts.filter(product => product.tenantId === tenantId);
};

// Top Deals Product Interface
export interface TopDealProduct {
  id: number;
  name: string;
  slug: string;
  originalPrice: number;
  salePrice: number;
  discount: number;
  image: string;
  rating: number;
  reviews: number;
  badge?: string;
  tenantId: string;
}

// Hot Picks Product Interface
export interface HotPickProduct {
  id: number;
  name: string;
  slug: string;
  price: number;
  salePrice?: number;
  image: string;
  rating: number;
  reviews: number;
  badge: string;
  trending: boolean;
  tenantId: string;
}

// Top Deals Products by Tenant
export const topDealsProducts: TopDealProduct[] = [
  // TechHub Electronics (tenant ID: 20) - Tech & Electronics focused deals
  {
    id: 1,
    name: 'Premium Wireless Headphones',
    slug: 'premium-wireless-headphones',
    originalPrice: 199.99,
    salePrice: 149.99,
    discount: 25,
    image: '/images/products/headphones.jpg',
    rating: 4.8,
    reviews: 324,
    badge: 'Sale',
    tenantId: '20'
  },
  {
    id: 2,
    name: 'Smart Fitness Watch',
    slug: 'smart-fitness-watch',
    originalPrice: 399.99,
    salePrice: 299.99,
    discount: 25,
    image: '/images/products/smartwatch.jpg',
    rating: 4.6,
    reviews: 189,
    badge: 'Sale',
    tenantId: '20'
  },
  {
    id: 3,
    name: 'Portable Bluetooth Speaker',
    slug: 'portable-bluetooth-speaker',
    originalPrice: 79.99,
    salePrice: 59.99,
    discount: 25,
    image: '/images/products/speaker.jpg',
    rating: 4.7,
    reviews: 256,
    badge: 'Sale',
    tenantId: '20'
  },
  {
    id: 4,
    name: 'Wireless Charging Pad',
    slug: 'wireless-charging-pad',
    originalPrice: 69.99,
    salePrice: 49.99,
    discount: 29,
    image: '/images/products/charger.jpg',
    rating: 4.5,
    reviews: 143,
    badge: 'Sale',
    tenantId: '20'
  },
  {
    id: 5,
    name: 'Gaming Mechanical Keyboard',
    slug: 'gaming-mechanical-keyboard',
    originalPrice: 159.99,
    salePrice: 119.99,
    discount: 25,
    image: '/images/products/gaming-keyboard.jpg',
    rating: 4.7,
    reviews: 412,
    badge: 'Sale',
    tenantId: '20'
  },
  {
    id: 6,
    name: 'HD Webcam with Microphone',
    slug: 'hd-webcam-microphone',
    originalPrice: 89.99,
    salePrice: 64.99,
    discount: 28,
    image: '/images/products/webcam.jpg',
    rating: 4.4,
    reviews: 98,
    badge: 'Sale',
    tenantId: '20'
  },
  {
    id: 7,
    name: '4K Gaming Monitor',
    slug: '4k-gaming-monitor',
    originalPrice: 599.99,
    salePrice: 429.99,
    discount: 28,
    image: '/images/products/gaming-monitor.jpg',
    rating: 4.6,
    reviews: 234,
    badge: 'Sale',
    tenantId: '20'
  },
  {
    id: 8,
    name: 'Laptop Stand with Cooling',
    slug: 'laptop-stand-cooling',
    originalPrice: 79.99,
    salePrice: 54.99,
    discount: 31,
    image: '/images/products/laptop-stand.jpg',
    rating: 4.3,
    reviews: 167,
    badge: 'Sale',
    tenantId: '20'
  },

  // StyleHub Fashion (tenant ID: 19) - Fashion & Lifestyle deals
  {
    id: 9,
    name: 'Designer Leather Handbag',
    slug: 'designer-leather-handbag',
    originalPrice: 189.99,
    salePrice: 129.99,
    discount: 32,
    image: '/images/products/handbag.jpg',
    rating: 4.7,
    reviews: 298,
    badge: 'Sale',
    tenantId: '19'
  },
  {
    id: 10,
    name: 'Premium Running Shoes',
    slug: 'premium-running-shoes',
    originalPrice: 149.99,
    salePrice: 99.99,
    discount: 33,
    image: '/images/products/running-shoes.jpg',
    rating: 4.6,
    reviews: 445,
    badge: 'Sale',
    tenantId: '19'
  },
  {
    id: 11,
    name: 'Luxury Watch Collection',
    slug: 'luxury-watch-collection',
    originalPrice: 249.99,
    salePrice: 172.99,
    discount: 31,
    image: '/images/products/luxury-watch.jpg',
    rating: 4.8,
    reviews: 187,
    badge: 'Sale',
    tenantId: '19'
  },
  {
    id: 12,
    name: 'Cashmere Winter Scarf',
    slug: 'cashmere-winter-scarf',
    originalPrice: 89.99,
    salePrice: 59.99,
    discount: 33,
    image: '/images/products/scarf.jpg',
    rating: 4.4,
    reviews: 123,
    badge: 'Sale',
    tenantId: '19'
  },
  {
    id: 13,
    name: 'Designer Denim Jacket',
    slug: 'designer-denim-jacket',
    originalPrice: 119.99,
    salePrice: 79.99,
    discount: 33,
    image: '/images/products/denim-jacket.jpg',
    rating: 4.5,
    reviews: 234,
    badge: 'Sale',
    tenantId: '19'
  },
  {
    id: 14,
    name: 'Premium Leather Boots',
    slug: 'premium-leather-boots',
    originalPrice: 229.99,
    salePrice: 159.99,
    discount: 30,
    image: '/images/products/leather-boots.jpg',
    rating: 4.7,
    reviews: 356,
    badge: 'Sale',
    tenantId: '19'
  },
  {
    id: 15,
    name: 'Silk Evening Dress',
    slug: 'silk-evening-dress',
    originalPrice: 249.99,
    salePrice: 172.99,
    discount: 31,
    image: '/images/products/evening-dress.jpg',
    rating: 4.8,
    reviews: 189,
    badge: 'Sale',
    tenantId: '19'
  },
  {
    id: 16,
    name: 'Designer Sunglasses',
    slug: 'designer-sunglasses',
    originalPrice: 199.99,
    salePrice: 139.99,
    discount: 30,
    image: '/images/products/sunglasses.jpg',
    rating: 4.6,
    reviews: 267,
    badge: 'Sale',
    tenantId: '19'
  },

  // HomeHub Essentials (tenant ID: 21) - Home & Furniture deals
  {
    id: 17,
    name: 'Ergonomic Office Chair',
    slug: 'ergonomic-office-chair',
    originalPrice: 249.99,
    salePrice: 167.99,
    discount: 33,
    image: '/images/products/office-chair.jpg',
    rating: 4.5,
    reviews: 167,
    badge: 'Sale',
    tenantId: '21'
  },
  {
    id: 18,
    name: 'LED Desk Lamp with USB',
    slug: 'led-desk-lamp-usb',
    originalPrice: 49.99,
    salePrice: 34.99,
    discount: 30,
    image: '/images/products/desk-lamp.jpg',
    rating: 4.3,
    reviews: 89,
    badge: 'Sale',
    tenantId: '21'
  },
  {
    id: 19,
    name: 'Memory Foam Mattress Queen',
    slug: 'memory-foam-mattress-queen',
    originalPrice: 249.99,
    salePrice: 167.99,
    discount: 33,
    image: '/images/products/mattress.jpg',
    rating: 4.7,
    reviews: 234,
    badge: 'Sale',
    tenantId: '21'
  },
  {
    id: 20,
    name: 'Modern Coffee Table',
    slug: 'modern-coffee-table',
    originalPrice: 249.99,
    salePrice: 179.99,
    discount: 28,
    image: '/images/products/coffee-table.jpg',
    rating: 4.4,
    reviews: 156,
    badge: 'Sale',
    tenantId: '21'
  },
  {
    id: 21,
    name: 'Professional Blender Pro',
    slug: 'professional-blender-pro',
    originalPrice: 199.99,
    salePrice: 139.99,
    discount: 30,
    image: '/images/products/blender.jpg',
    rating: 4.6,
    reviews: 267,
    badge: 'Sale',
    tenantId: '21'
  },
  {
    id: 22,
    name: 'Stainless Steel Cookware Set',
    slug: 'stainless-steel-cookware-set',
    originalPrice: 249.99,
    salePrice: 167.99,
    discount: 33,
    image: '/images/products/cookware.jpg',
    rating: 4.8,
    reviews: 189,
    badge: 'Sale',
    tenantId: '21'
  }
];

export const promotionalBanners: PromotionalBanner[] = [
  {
    id: 'promo-1',
    title: 'Flash Sale',
    subtitle: 'Limited Time Offer',
    description: 'Get up to 50% off on selected electronics. Hurry, sale ends soon!',
    image: '/images/promotions/flash-sale-electronics.jpg',
    discountPercentage: 50,
    validUntil: '2024-07-31T23:59:59Z',
    buttonText: 'Shop Flash Sale',
    buttonLink: '/deals/flash-sale',
    backgroundColor: 'bg-gradient-to-r from-red-500 to-orange-500',
    isActive: true,
    tenantId: defaultTenantId,
  },
  {
    id: 'promo-2',
    title: 'Free Shipping Weekend',
    subtitle: 'No Minimum Order',
    description: 'Enjoy free shipping on all orders this weekend. No minimum purchase required!',
    image: '/images/promotions/free-shipping-weekend.jpg',
    validUntil: '2024-07-28T23:59:59Z',
    buttonText: 'Shop Now',
    buttonLink: '/products',
    backgroundColor: 'bg-gradient-to-r from-green-500 to-teal-500',
    isActive: true,
    tenantId: defaultTenantId,
  },
];

// Hot Picks Products by Tenant
export const hotPicksProducts: HotPickProduct[] = [
  // TechHub Electronics (tenant ID: 20) - Tech & Electronics focused hot picks
  {
    id: 1,
    name: 'Premium Wireless Headphones',
    slug: 'premium-wireless-headphones',
    price: 149.99,
    salePrice: 119.99,
    image: '/images/products/headphones.jpg',
    rating: 4.8,
    reviews: 324,
    badge: 'Hot',
    trending: true,
    tenantId: '20'
  },
  {
    id: 2,
    name: 'Smart Fitness Watch',
    slug: 'smart-fitness-watch',
    price: 299.99,
    salePrice: 249.99,
    image: '/images/products/smartwatch.jpg',
    rating: 4.6,
    reviews: 189,
    badge: 'Trending',
    trending: true,
    tenantId: '20'
  },
  {
    id: 3,
    name: 'Gaming Mechanical Keyboard',
    slug: 'gaming-mechanical-keyboard',
    price: 119.99,
    image: '/images/products/gaming-keyboard.jpg',
    rating: 4.7,
    reviews: 412,
    badge: 'Hot',
    trending: true,
    tenantId: '20'
  },
  {
    id: 4,
    name: '4K Gaming Monitor',
    slug: '4k-gaming-monitor',
    price: 429.99,
    salePrice: 379.99,
    image: '/images/products/gaming-monitor.jpg',
    rating: 4.6,
    reviews: 234,
    badge: 'Trending',
    trending: true,
    tenantId: '20'
  },
  {
    id: 1,
    name: 'iPhone 15 Pro Max',
    slug: 'iphone-15-pro-max',
    price: 1199.99,
    image: '/images/products/iphone.jpg',
    rating: 4.9,
    reviews: 1247,
    badge: 'Hot',
    trending: true,
    tenantId: '20'
  },
  {
    id: 2,
    name: 'MacBook Air M3',
    slug: 'macbook-air-m3',
    price: 1299.99,
    salePrice: 1199.99,
    image: '/images/products/macbook.jpg',
    rating: 4.8,
    reviews: 892,
    badge: 'Trending',
    trending: true,
    tenantId: '20'
  },
  {
    id: 3,
    name: 'AirPods Pro 2nd Gen',
    slug: 'airpods-pro-2nd-gen',
    price: 249.99,
    image: '/images/products/airpods.jpg',
    rating: 4.7,
    reviews: 2156,
    badge: 'Hot',
    trending: true,
    tenantId: '20'
  },
  {
    id: 4,
    name: 'Samsung Galaxy S24 Ultra',
    slug: 'samsung-galaxy-s24-ultra',
    price: 1199.99,
    salePrice: 1099.99,
    image: '/images/products/samsung.jpg',
    rating: 4.6,
    reviews: 743,
    badge: 'Trending',
    trending: true,
    tenantId: '20'
  },
  {
    id: 5,
    name: 'Sony WH-1000XM5 Headphones',
    slug: 'sony-wh-1000xm5',
    price: 399.99,
    image: '/images/products/sony-headphones.jpg',
    rating: 4.8,
    reviews: 1534,
    badge: 'Hot',
    trending: true,
    tenantId: '20'
  },
  {
    id: 6,
    name: 'iPad Pro 12.9" M4',
    slug: 'ipad-pro-129-m4',
    price: 1099.99,
    salePrice: 999.99,
    image: '/images/products/ipad.jpg',
    rating: 4.7,
    reviews: 567,
    badge: 'Trending',
    trending: true,
    tenantId: '20'
  },
  {
    id: 7,
    name: 'Nintendo Switch OLED',
    slug: 'nintendo-switch-oled',
    price: 349.99,
    image: '/images/products/nintendo.jpg',
    rating: 4.6,
    reviews: 1823,
    badge: 'Hot',
    trending: true,
    tenantId: '20'
  },
  {
    id: 8,
    name: 'Tesla Model Y Accessories Kit',
    slug: 'tesla-model-y-kit',
    price: 199.99,
    salePrice: 149.99,
    image: '/images/products/tesla-kit.jpg',
    rating: 4.5,
    reviews: 234,
    badge: 'Trending',
    trending: true,
    tenantId: '20'
  },

  // StyleHub Fashion (tenant ID: 19) - Fashion & Lifestyle hot picks
  {
    id: 5,
    name: 'Designer Leather Handbag',
    slug: 'designer-leather-handbag',
    price: 129.99,
    image: '/images/products/handbag.jpg',
    rating: 4.7,
    reviews: 298,
    badge: 'Hot',
    trending: true,
    tenantId: '19'
  },
  {
    id: 6,
    name: 'Premium Running Shoes',
    slug: 'premium-running-shoes',
    price: 99.99,
    salePrice: 79.99,
    image: '/images/products/running-shoes.jpg',
    rating: 4.6,
    reviews: 445,
    badge: 'Trending',
    trending: true,
    tenantId: '19'
  },
  {
    id: 7,
    name: 'Luxury Watch Collection',
    slug: 'luxury-watch-collection',
    price: 172.99,
    image: '/images/products/luxury-watch.jpg',
    rating: 4.8,
    reviews: 187,
    badge: 'Hot',
    trending: true,
    tenantId: '19'
  },
  {
    id: 8,
    name: 'Designer Sunglasses',
    slug: 'designer-sunglasses',
    price: 139.99,
    salePrice: 119.99,
    image: '/images/products/sunglasses.jpg',
    rating: 4.6,
    reviews: 267,
    badge: 'Trending',
    trending: true,
    tenantId: '19'
  },
  {
    id: 9,
    name: 'Designer Sneaker Collection',
    slug: 'designer-sneaker-collection',
    price: 299.99,
    salePrice: 249.99,
    image: '/images/products/designer-sneakers.jpg',
    rating: 4.8,
    reviews: 892,
    badge: 'Hot',
    trending: true,
    tenantId: '19'
  },
  {
    id: 10,
    name: 'Luxury Smartwatch Pro',
    slug: 'luxury-smartwatch-pro',
    price: 599.99,
    salePrice: 499.99,
    image: '/images/products/luxury-smartwatch.jpg',
    rating: 4.7,
    reviews: 456,
    badge: 'Trending',
    trending: true,
    tenantId: '19'
  },
  {
    id: 11,
    name: 'Premium Wireless Earbuds',
    slug: 'premium-wireless-earbuds',
    price: 179.99,
    salePrice: 149.99,
    image: '/images/products/wireless-earbuds.jpg',
    rating: 4.6,
    reviews: 1234,
    badge: 'Hot',
    trending: true,
    tenantId: '19'
  },
  {
    id: 12,
    name: 'Designer Handbag Collection',
    slug: 'designer-handbag-collection',
    price: 399.99,
    salePrice: 299.99,
    image: '/images/products/designer-handbag.jpg',
    rating: 4.9,
    reviews: 567,
    badge: 'Trending',
    trending: true,
    tenantId: '19'
  },
  {
    id: 13,
    name: 'Limited Edition Jacket',
    slug: 'limited-edition-jacket',
    price: 249.99,
    salePrice: 199.99,
    image: '/images/products/limited-jacket.jpg',
    rating: 4.8,
    reviews: 345,
    badge: 'Hot',
    trending: true,
    tenantId: '19'
  },
  {
    id: 14,
    name: 'Luxury Perfume Set',
    slug: 'luxury-perfume-set',
    price: 159.99,
    salePrice: 129.99,
    image: '/images/products/perfume-set.jpg',
    rating: 4.7,
    reviews: 678,
    badge: 'Trending',
    trending: true,
    tenantId: '19'
  },
  {
    id: 15,
    name: 'Premium Sunglasses',
    slug: 'premium-sunglasses',
    price: 199.99,
    salePrice: 149.99,
    image: '/images/products/premium-sunglasses.jpg',
    rating: 4.6,
    reviews: 234,
    badge: 'Hot',
    trending: true,
    tenantId: '19'
  },
  {
    id: 16,
    name: 'Designer Watch Collection',
    slug: 'designer-watch-collection',
    price: 899.99,
    salePrice: 699.99,
    image: '/images/products/designer-watch.jpg',
    rating: 4.9,
    reviews: 456,
    badge: 'Trending',
    trending: true,
    tenantId: '19'
  },

  // HomeHub Essentials (tenant ID: 21) - Home & Furniture hot picks
  {
    id: 9,
    name: 'Ergonomic Office Chair',
    slug: 'ergonomic-office-chair',
    price: 167.99,
    image: '/images/products/office-chair.jpg',
    rating: 4.5,
    reviews: 167,
    badge: 'Hot',
    trending: true,
    tenantId: '21'
  },
  {
    id: 10,
    name: 'Professional Blender Pro',
    slug: 'professional-blender-pro',
    price: 139.99,
    salePrice: 119.99,
    image: '/images/products/blender.jpg',
    rating: 4.6,
    reviews: 267,
    badge: 'Trending',
    trending: true,
    tenantId: '21'
  },
  {
    id: 11,
    name: 'Modern Coffee Table',
    slug: 'modern-coffee-table',
    price: 179.99,
    image: '/images/products/coffee-table.jpg',
    rating: 4.4,
    reviews: 156,
    badge: 'Hot',
    trending: true,
    tenantId: '21'
  },
  {
    id: 12,
    name: 'Smart Home Security System',
    slug: 'smart-home-security-system',
    price: 279.99,
    salePrice: 239.99,
    image: '/images/products/security-system.jpg',
    rating: 4.6,
    reviews: 145,
    badge: 'Trending',
    trending: true,
    tenantId: '21'
  },
  {
    id: 17,
    name: 'Smart Home Hub Pro',
    slug: 'smart-home-hub-pro',
    price: 199.99,
    image: '/images/products/smart-home-hub.jpg',
    rating: 4.5,
    reviews: 678,
    badge: 'Trending',
    trending: true,
    tenantId: '21'
  },
  {
    id: 18,
    name: 'Premium Air Purifier',
    slug: 'premium-air-purifier',
    price: 299.99,
    salePrice: 249.99,
    image: '/images/products/air-purifier.jpg',
    rating: 4.7,
    reviews: 456,
    badge: 'Hot',
    trending: true,
    tenantId: '21'
  },
  {
    id: 19,
    name: 'Robot Vacuum Cleaner',
    slug: 'robot-vacuum-cleaner',
    price: 399.99,
    salePrice: 329.99,
    image: '/images/products/robot-vacuum.jpg',
    rating: 4.6,
    reviews: 789,
    badge: 'Trending',
    trending: true,
    tenantId: '21'
  },
  {
    id: 20,
    name: 'Smart Thermostat',
    slug: 'smart-thermostat',
    price: 149.99,
    salePrice: 119.99,
    image: '/images/products/smart-thermostat.jpg',
    rating: 4.8,
    reviews: 234,
    badge: 'Hot',
    trending: true,
    tenantId: '21'
  },
  {
    id: 21,
    name: 'Premium Coffee Machine',
    slug: 'premium-coffee-machine',
    price: 599.99,
    salePrice: 499.99,
    image: '/images/products/coffee-machine.jpg',
    rating: 4.9,
    reviews: 567,
    badge: 'Trending',
    trending: true,
    tenantId: '21'
  },
  {
    id: 22,
    name: 'Smart Security Camera',
    slug: 'smart-security-camera',
    price: 179.99,
    salePrice: 149.99,
    image: '/images/products/security-camera.jpg',
    rating: 4.6,
    reviews: 345,
    badge: 'Hot',
    trending: true,
    tenantId: '21'
  },
  {
    id: 23,
    name: 'Luxury Bedding Set',
    slug: 'luxury-bedding-set',
    price: 249.99,
    salePrice: 199.99,
    image: '/images/products/luxury-bedding.jpg',
    rating: 4.8,
    reviews: 456,
    badge: 'Trending',
    trending: true,
    tenantId: '21'
  },
  {
    id: 24,
    name: 'Smart Garden System',
    slug: 'smart-garden-system',
    price: 299.99,
    salePrice: 249.99,
    image: '/images/products/smart-garden.jpg',
    rating: 4.7,
    reviews: 234,
    badge: 'Hot',
    trending: true,
    tenantId: '21'
  }
];

export const brandShowcase: BrandShowcase[] = [
  {
    id: 'brand-1',
    name: 'AudioTech Pro',
    logo: '/images/brands/audiotech-pro-logo.png',
    description: 'Premium audio equipment and accessories',
    productCount: 45,
    isPartner: true,
    website: 'https://audiotechpro.com',
    tenantId: defaultTenantId,
  },
  {
    id: 'brand-2',
    name: 'FitTech',
    logo: '/images/brands/fittech-logo.png',
    description: 'Smart fitness and health monitoring devices',
    productCount: 23,
    isPartner: true,
    website: 'https://fittech.com',
    tenantId: defaultTenantId,
  },
  {
    id: 'brand-3',
    name: 'EcoWear',
    logo: '/images/brands/ecowear-logo.png',
    description: 'Sustainable and eco-friendly fashion',
    productCount: 67,
    isPartner: true,
    website: 'https://ecowear.com',
    tenantId: defaultTenantId,
  },
  {
    id: 'brand-4',
    name: 'PhotoPro',
    logo: '/images/brands/photopro-logo.png',
    description: 'Professional photography equipment',
    productCount: 34,
    isPartner: true,
    website: 'https://photopro.com',
    tenantId: defaultTenantId,
  },
  {
    id: 'brand-5',
    name: 'ComfortPlus',
    logo: '/images/brands/comfortplus-logo.png',
    description: 'Ergonomic furniture and office solutions',
    productCount: 28,
    isPartner: true,
    website: 'https://comfortplus.com',
    tenantId: defaultTenantId,
  },
  {
    id: 'brand-6',
    name: 'BrewMaster',
    logo: '/images/brands/brewmaster-logo.png',
    description: 'Premium coffee makers and accessories',
    productCount: 19,
    isPartner: true,
    website: 'https://brewmaster.com',
    tenantId: defaultTenantId,
  },
];

export const customerTestimonials: CustomerTestimonial[] = [
  {
    id: 'testimonial-1',
    customerName: 'Priya Sharma',
    customerAvatar: '/images/avatars/customers/priya-sharma.jpg',
    rating: 5,
    review: 'Amazing quality and fast delivery! The wireless headphones exceeded my expectations. Crystal clear sound and comfortable fit.',
    productName: 'Premium Wireless Headphones',
    location: 'Mumbai, Maharashtra',
    date: '2024-06-15',
    isVerified: true,
    tenantId: defaultTenantId,
  },
  {
    id: 'testimonial-2',
    customerName: 'Rajesh Kumar',
    customerAvatar: '/images/avatars/customers/rajesh-kumar.jpg',
    rating: 5,
    review: 'Excellent customer service and product quality. The fitness watch helps me track my daily activities perfectly.',
    productName: 'Smart Fitness Watch',
    location: 'Delhi, NCR',
    date: '2024-06-12',
    isVerified: true,
    tenantId: defaultTenantId,
  },
  {
    id: 'testimonial-3',
    customerName: 'Anita Patel',
    customerAvatar: '/images/avatars/customers/anita-patel.jpg',
    rating: 4,
    review: 'Great shopping experience! The organic cotton t-shirt is so comfortable and the quality is outstanding.',
    productName: 'Organic Cotton T-Shirt',
    location: 'Ahmedabad, Gujarat',
    date: '2024-06-10',
    isVerified: true,
    tenantId: defaultTenantId,
  },
  {
    id: 'testimonial-4',
    customerName: 'Vikram Singh',
    customerAvatar: '/images/avatars/customers/vikram-singh.jpg',
    rating: 5,
    review: 'Professional grade camera lens at an affordable price. Perfect for my photography business. Highly recommended!',
    productName: 'Professional Camera Lens',
    location: 'Jaipur, Rajasthan',
    date: '2024-06-08',
    isVerified: true,
    tenantId: defaultTenantId,
  },
];

export const newsletterCTA: NewsletterCTA = {
  id: 'newsletter-1',
  title: 'Stay Updated with Latest Deals',
  subtitle: 'Join Our Newsletter',
  description: 'Get exclusive offers, new product announcements, and insider deals delivered to your inbox.',
  backgroundImage: '/images/newsletter/newsletter-bg.jpg',
  buttonText: 'Subscribe Now',
  placeholderText: 'Enter your email address',
  benefits: [
    'Exclusive early access to sales',
    'New product announcements',
    'Personalized recommendations',
    'Special member-only discounts',
  ],
  tenantId: defaultTenantId,
};



export const getCategoryShowcaseByTenant = (tenantId: string = defaultTenantId): CategoryShowcase[] => {
  return categoryShowcase.filter(category => category.tenantId === tenantId && category.isActive);
};

export const getPromotionalBannersByTenant = (tenantId: string = defaultTenantId): PromotionalBanner[] => {
  return promotionalBanners.filter(banner => banner.tenantId === tenantId && banner.isActive);
};

export const getBrandShowcaseByTenant = (tenantId: string = defaultTenantId): BrandShowcase[] => {
  return brandShowcase.filter(brand => brand.tenantId === tenantId);
};

export const getCustomerTestimonialsByTenant = (tenantId: string = defaultTenantId): CustomerTestimonial[] => {
  return customerTestimonials.filter(testimonial => testimonial.tenantId === tenantId);
};

export const getNewsletterCTAByTenant = (tenantId: string = defaultTenantId): NewsletterCTA | null => {
  return newsletterCTA.tenantId === tenantId ? newsletterCTA : null;
};

export const getTopDealsByTenant = (tenantId: string = defaultTenantId): TopDealProduct[] => {
  return topDealsProducts.filter(product => product.tenantId === tenantId);
};

export const getTrendingProductsByTenant = (tenantId: string = defaultTenantId): TopDealProduct[] => {
  return topDealsProducts.filter(product => product.tenantId === tenantId && product.trending === true);
};

export const getHotPicksByTenant = (tenantId: string = defaultTenantId): HotPickProduct[] => {
  return hotPicksProducts.filter(product => String(product.tenantId) === String(tenantId));
};

