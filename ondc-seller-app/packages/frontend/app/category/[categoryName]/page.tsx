'use client';

import React, { useState, useEffect, Suspense } from 'react';
import { useParams, useSearchParams } from 'next/navigation';
import { useTenant } from '@/contexts/TenantContext';
import ProductListingPage, { Product, ProductListingConfig } from '@/components/common/ProductListingPage';
import { PAGE_CONFIGS, getProductImageUrl, createBreadcrumbs, createSubcategoryFilters } from '@/utils/productListingHelpers';

// Mock subcategory data for different categories
const subcategoryData: { [key: string]: { [key: string]: any[] } } = {
  'electronics': {
    'smartphones': [
      {
        id: 301,
        name: 'iPhone 15 Pro',
        slug: 'iphone-15-pro',
        originalPrice: 134900,
        salePrice: 129900,
        discount: 4,
        rating: 4.8,
        reviews: 2345,
        subcategory: 'smartphones'
      },
      {
        id: 302,
        name: 'Samsung Galaxy S24',
        slug: 'samsung-galaxy-s24',
        originalPrice: 89999,
        salePrice: 79999,
        discount: 11,
        rating: 4.6,
        reviews: 1567,
        subcategory: 'smartphones'
      },
      {
        id: 303,
        name: 'Google Pixel 8 Pro',
        slug: 'google-pixel-8-pro',
        originalPrice: 84999,
        salePrice: 74999,
        discount: 12,
        rating: 4.7,
        reviews: 892,
        subcategory: 'smartphones'
      }
    ],
    'laptops': [
      {
        id: 304,
        name: 'MacBook Air M3',
        slug: 'macbook-air-m3',
        originalPrice: 134900,
        salePrice: 124900,
        discount: 7,
        rating: 4.9,
        reviews: 1234,
        subcategory: 'laptops'
      },
      {
        id: 305,
        name: 'Dell XPS 13',
        slug: 'dell-xps-13',
        originalPrice: 119999,
        salePrice: 109999,
        discount: 8,
        rating: 4.6,
        reviews: 678,
        subcategory: 'laptops'
      },
      {
        id: 306,
        name: 'ThinkPad X1 Carbon',
        slug: 'thinkpad-x1-carbon',
        originalPrice: 149999,
        salePrice: 134999,
        discount: 10,
        rating: 4.7,
        reviews: 456,
        subcategory: 'laptops'
      }
    ],
    'headphones': [
      {
        id: 307,
        name: 'Sony WH-1000XM5',
        slug: 'sony-wh-1000xm5',
        originalPrice: 34990,
        salePrice: 29990,
        discount: 14,
        rating: 4.8,
        reviews: 1567,
        subcategory: 'headphones'
      },
      {
        id: 308,
        name: 'Bose QuietComfort 45',
        slug: 'bose-quietcomfort-45',
        originalPrice: 32900,
        salePrice: 27900,
        discount: 15,
        rating: 4.7,
        reviews: 892,
        subcategory: 'headphones'
      },
      {
        id: 309,
        name: 'AirPods Max',
        slug: 'airpods-max',
        originalPrice: 59900,
        salePrice: 54900,
        discount: 8,
        rating: 4.6,
        reviews: 1234,
        subcategory: 'headphones'
      }
    ]
  },
  'fashion': {
    'mens-clothing': [
      {
        id: 310,
        name: 'Ralph Lauren Polo Shirt',
        slug: 'ralph-lauren-polo-shirt',
        originalPrice: 8999,
        salePrice: 6999,
        discount: 22,
        rating: 4.5,
        reviews: 456,
        subcategory: 'mens-clothing'
      },
      {
        id: 311,
        name: 'Levi\'s 501 Original Jeans',
        slug: 'levis-501-original-jeans',
        originalPrice: 7999,
        salePrice: 5999,
        discount: 25,
        rating: 4.6,
        reviews: 789,
        subcategory: 'mens-clothing'
      },
      {
        id: 312,
        name: 'Nike Air Force 1',
        slug: 'nike-air-force-1',
        originalPrice: 9999,
        salePrice: 7999,
        discount: 20,
        rating: 4.7,
        reviews: 1234,
        subcategory: 'mens-clothing'
      }
    ],
    'womens-clothing': [
      {
        id: 313,
        name: 'Zara Midi Dress',
        slug: 'zara-midi-dress',
        originalPrice: 5999,
        salePrice: 4499,
        discount: 25,
        rating: 4.4,
        reviews: 567,
        subcategory: 'womens-clothing'
      },
      {
        id: 314,
        name: 'H&M Blazer',
        slug: 'hm-blazer',
        originalPrice: 4999,
        salePrice: 3499,
        discount: 30,
        rating: 4.3,
        reviews: 345,
        subcategory: 'womens-clothing'
      },
      {
        id: 315,
        name: 'Adidas Ultraboost 22',
        slug: 'adidas-ultraboost-22',
        originalPrice: 17999,
        salePrice: 14999,
        discount: 17,
        rating: 4.6,
        reviews: 892,
        subcategory: 'womens-clothing'
      }
    ],
    'accessories': [
      {
        id: 316,
        name: 'Michael Kors Watch',
        slug: 'michael-kors-watch',
        originalPrice: 19999,
        salePrice: 15999,
        discount: 20,
        rating: 4.5,
        reviews: 234,
        subcategory: 'accessories'
      },
      {
        id: 317,
        name: 'Ray-Ban Aviator Sunglasses',
        slug: 'ray-ban-aviator-sunglasses',
        originalPrice: 15999,
        salePrice: 12999,
        discount: 19,
        rating: 4.7,
        reviews: 678,
        subcategory: 'accessories'
      },
      {
        id: 318,
        name: 'Coach Leather Wallet',
        slug: 'coach-leather-wallet',
        originalPrice: 12999,
        salePrice: 9999,
        discount: 23,
        rating: 4.6,
        reviews: 456,
        subcategory: 'accessories'
      }
    ]
  }
};

// Available subcategories for each category
const subcategories: { [key: string]: Array<{ id: string; name: string }> } = {
  'electronics': [
    { id: 'smartphones', name: 'Smartphones' },
    { id: 'laptops', name: 'Laptops' },
    { id: 'headphones', name: 'Headphones' }
  ],
  'fashion': [
    { id: 'mens-clothing', name: 'Men\'s Clothing' },
    { id: 'womens-clothing', name: 'Women\'s Clothing' },
    { id: 'accessories', name: 'Accessories' }
  ]
};

// Convert raw product data to Product interface
function convertToProducts(rawProducts: any[]): Product[] {
  return rawProducts.map(product => ({
    id: product.id,
    name: product.name,
    slug: product.slug,
    originalPrice: product.originalPrice,
    salePrice: product.salePrice,
    discount: product.discount,
    image: getProductImageUrl(product.name, product.id),
    category: product.subcategory === 'smartphones' || product.subcategory === 'laptops' || product.subcategory === 'headphones' ? 'Electronics' : 'Fashion',
    rating: product.rating,
    reviewCount: product.reviews,
    badge: product.discount && product.discount > 15 ? 'Sale' : undefined,
    brand: product.name.split(' ')[0], // Use first word as brand
    inStock: true
  }));
}

function CategoryPageContent() {
  const params = useParams();
  const searchParams = useSearchParams();
  const { tenantId } = useTenant();
  
  const categoryName = params.categoryName as string;
  const activeSubcategory = searchParams.get('subcategory') || '';
  
  const [products, setProducts] = useState<Product[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Get all products for the category
  const getAllCategoryProducts = (category: string): any[] => {
    const categoryData = subcategoryData[category];
    if (!categoryData) return [];
    
    return Object.values(categoryData).flat();
  };

  // Get products for specific subcategory
  const getSubcategoryProducts = (category: string, subcategory: string): any[] => {
    const categoryData = subcategoryData[category];
    if (!categoryData || !categoryData[subcategory]) return [];
    
    return categoryData[subcategory];
  };

  useEffect(() => {
    setIsLoading(true);
    
    let rawProducts: any[] = [];
    if (activeSubcategory) {
      rawProducts = getSubcategoryProducts(categoryName, activeSubcategory);
    } else {
      rawProducts = getAllCategoryProducts(categoryName);
    }
    
    const convertedProducts = convertToProducts(rawProducts);
    
    setTimeout(() => {
      setProducts(convertedProducts);
      setIsLoading(false);
    }, 300);
  }, [categoryName, activeSubcategory]);

  const handleSubcategoryChange = (subcategoryId: string) => {
    const url = new URL(window.location.href);
    if (subcategoryId) {
      url.searchParams.set('subcategory', subcategoryId);
    } else {
      url.searchParams.delete('subcategory');
    }
    window.history.pushState({}, '', url.toString());
    
    // Trigger re-render by updating the URL
    window.location.href = url.toString();
  };

  // Create configuration for the page
  const config: ProductListingConfig = {
    ...PAGE_CONFIGS['subcategory'],
    title: activeSubcategory 
      ? subcategories[categoryName]?.find(sub => sub.id === activeSubcategory)?.name || 'Products'
      : `${categoryName.charAt(0).toUpperCase() + categoryName.slice(1)} Products`,
    subtitle: activeSubcategory 
      ? `Discover amazing ${subcategories[categoryName]?.find(sub => sub.id === activeSubcategory)?.name.toLowerCase()}`
      : `Explore our ${categoryName} collection`,
    breadcrumbs: createBreadcrumbs([
      { name: 'Categories', href: '/categories' },
      { name: categoryName.charAt(0).toUpperCase() + categoryName.slice(1), href: `/category/${categoryName}` },
      ...(activeSubcategory ? [{ name: subcategories[categoryName]?.find(sub => sub.id === activeSubcategory)?.name || 'Products' }] : [])
    ]),
    subcategoryFilters: subcategories[categoryName] ? createSubcategoryFilters(
      subcategories[categoryName],
      convertToProducts(getAllCategoryProducts(categoryName))
    ) : undefined,
    activeSubcategory
  };

  return (
    <ProductListingPage
      products={products}
      config={config}
      isLoading={isLoading}
      onSubcategoryChange={handleSubcategoryChange}
    />
  );
}

export default function CategoryPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading products...</p>
        </div>
      </div>
    }>
      <CategoryPageContent />
    </Suspense>
  );
}
