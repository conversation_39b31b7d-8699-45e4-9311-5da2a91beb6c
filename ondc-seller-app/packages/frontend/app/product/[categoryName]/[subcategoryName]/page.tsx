'use client';

import React, { useState, useEffect, useMemo } from 'react';
import { useParams, useSearchParams, useRouter } from 'next/navigation';
import Link from 'next/link';
import Image from 'next/image';
import {
  ArrowLeftIcon,
  HeartIcon,
  ShareIcon,
  StarIcon,
  ShoppingCartIcon,
  TruckIcon,
  ShieldCheckIcon,
  ArrowPathIcon,
  MinusIcon,
  PlusIcon,
} from '@heroicons/react/24/outline';
import { HeartIcon as HeartSolidIcon } from '@heroicons/react/24/solid';
import { useCart } from '@/context/CartContext';
import { useHydratedCartStore, useCartStore } from '@/stores/cartStore';
import { useTenant } from '@/contexts/TenantContext';
import toast from 'react-hot-toast';
import Breadcrumbs from '@/components/Breadcrumbs';
import { productCategories, getCategoryById, getSubcategoryById } from '@/data/categories';
import { findProductBySlug, SharedProduct, generateMockReviews } from '@/data/shared-products';

// Import the deals data to find the product
// We'll define the data here since it's not exported from the deals page

// Tenant-specific Top Deals Products (same as TopDeals component)
const topDealsProductsByTenant: { [key: string]: any[] } = {
  // TechHub Electronics - Tech & Electronics focused deals
  '20': [
    {
      id: 1,
      name: 'Premium Wireless Headphones',
      slug: 'premium-wireless-headphones',
      originalPrice: 8299,
      salePrice: 6199,
      discount: 25,
      image: '/images/products/headphones.jpg',
      rating: 4.8,
      reviews: 324,
      badge: 'Sale'
    },
    {
      id: 2,
      name: 'Smart Fitness Watch',
      slug: 'smart-fitness-watch',
      originalPrice: 4999,
      salePrice: 3749,
      discount: 25,
      image: '/images/products/smartwatch.jpg',
      rating: 4.6,
      reviews: 189,
      badge: 'Sale'
    },
    {
      id: 3,
      name: 'Portable Bluetooth Speaker',
      slug: 'portable-bluetooth-speaker',
      originalPrice: 2999,
      salePrice: 2249,
      discount: 25,
      image: '/images/products/speaker.jpg',
      rating: 4.5,
      reviews: 256,
      badge: 'Sale'
    },
    {
      id: 4,
      name: 'Wireless Charging Pad',
      slug: 'wireless-charging-pad',
      originalPrice: 2799,
      salePrice: 1999,
      discount: 29,
      image: '/images/products/charger.jpg',
      rating: 4.5,
      reviews: 143,
      badge: 'Sale'
    },
    {
      id: 5,
      name: 'Gaming Mechanical Keyboard',
      slug: 'gaming-mechanical-keyboard',
      originalPrice: 6399,
      salePrice: 4799,
      discount: 25,
      image: '/images/products/keyboard.jpg',
      rating: 4.7,
      reviews: 412,
      badge: 'Sale'
    },
    {
      id: 6,
      name: 'HD Webcam with Microphone',
      slug: 'hd-webcam-microphone',
      originalPrice: 3599,
      salePrice: 2599,
      discount: 28,
      image: '/images/products/webcam.jpg',
      rating: 4.4,
      reviews: 98,
      badge: 'Sale'
    },
    {
      id: 7,
      name: '4K Smart TV 55 inch',
      slug: '4k-smart-tv-55-inch',
      originalPrice: 9999,
      salePrice: 7199,
      discount: 28,
      image: '/images/products/smart-tv.jpg',
      rating: 4.6,
      reviews: 234,
      badge: 'Sale'
    },
    {
      id: 8,
      name: 'Laptop Stand with Cooling',
      slug: 'laptop-stand-cooling',
      originalPrice: 3199,
      salePrice: 2199,
      discount: 31,
      image: '/images/products/laptop-stand.jpg',
      rating: 4.3,
      reviews: 167,
      badge: 'Sale'
    }
  ],
  // StyleHub Fashion-3 - Fashion & Lifestyle deals
  '19': [
    {
      id: 9,
      name: 'Designer Leather Handbag',
      slug: 'designer-leather-handbag',
      originalPrice: 7599,
      salePrice: 5199,
      discount: 32,
      image: '/images/products/handbag.jpg',
      rating: 4.7,
      reviews: 298,
      badge: 'Sale'
    },
    {
      id: 10,
      name: 'Premium Running Shoes',
      slug: 'premium-running-shoes',
      originalPrice: 5999,
      salePrice: 3999,
      discount: 33,
      image: '/images/products/running-shoes.jpg',
      rating: 4.6,
      reviews: 445,
      badge: 'Sale'
    },
    {
      id: 11,
      name: 'Luxury Watch Collection',
      slug: 'luxury-watch-collection',
      originalPrice: 9999,
      salePrice: 6899,
      discount: 31,
      image: '/images/products/luxury-watch.jpg',
      rating: 4.8,
      reviews: 187,
      badge: 'Sale'
    },
    {
      id: 12,
      name: 'Cashmere Winter Scarf',
      slug: 'cashmere-winter-scarf',
      originalPrice: 3599,
      salePrice: 2399,
      discount: 33,
      image: '/images/products/scarf.jpg',
      rating: 4.4,
      reviews: 123,
      badge: 'Sale'
    },
    {
      id: 13,
      name: 'Designer Denim Jacket',
      slug: 'designer-denim-jacket',
      originalPrice: 4799,
      salePrice: 3199,
      discount: 33,
      image: '/images/products/denim-jacket.jpg',
      rating: 4.5,
      reviews: 234,
      badge: 'Sale'
    },
    {
      id: 14,
      name: 'Premium Leather Boots',
      slug: 'premium-leather-boots',
      originalPrice: 9199,
      salePrice: 6399,
      discount: 30,
      image: '/images/products/leather-boots.jpg',
      rating: 4.7,
      reviews: 356,
      badge: 'Sale'
    },
    {
      id: 15,
      name: 'Silk Evening Dress',
      slug: 'silk-evening-dress',
      originalPrice: 9999,
      salePrice: 6899,
      discount: 31,
      image: '/images/products/evening-dress.jpg',
      rating: 4.8,
      reviews: 189,
      badge: 'Sale'
    },
    {
      id: 16,
      name: 'Designer Sunglasses',
      slug: 'designer-sunglasses',
      originalPrice: 7999,
      salePrice: 5599,
      discount: 30,
      image: '/images/products/sunglasses.jpg',
      rating: 4.6,
      reviews: 267,
      badge: 'Sale'
    }
  ],
  // HomeHub Essentials - Home & Furniture deals
  '21': [
    {
      id: 17,
      name: 'Ergonomic Office Chair',
      slug: 'ergonomic-office-chair',
      originalPrice: 9999,
      salePrice: 6699,
      discount: 33,
      image: '/images/products/office-chair.jpg',
      rating: 4.5,
      reviews: 167,
      badge: 'Sale'
    },
    {
      id: 18,
      name: 'LED Desk Lamp with USB',
      slug: 'led-desk-lamp-usb',
      originalPrice: 1999,
      salePrice: 1399,
      discount: 30,
      image: '/images/products/desk-lamp.jpg',
      rating: 4.3,
      reviews: 89,
      badge: 'Sale'
    },
    {
      id: 19,
      name: 'Memory Foam Mattress Queen',
      slug: 'memory-foam-mattress-queen',
      originalPrice: 9999,
      salePrice: 6699,
      discount: 33,
      image: '/images/products/mattress.jpg',
      rating: 4.7,
      reviews: 234,
      badge: 'Sale'
    },
    {
      id: 20,
      name: 'Modern Coffee Table',
      slug: 'modern-coffee-table',
      originalPrice: 9999,
      salePrice: 7199,
      discount: 28,
      image: '/images/products/coffee-table.jpg',
      rating: 4.4,
      reviews: 145,
      badge: 'Sale'
    },
    {
      id: 21,
      name: 'Professional Blender Pro',
      slug: 'professional-blender-pro',
      originalPrice: 7999,
      salePrice: 5599,
      discount: 30,
      image: '/images/products/blender.jpg',
      rating: 4.6,
      reviews: 198,
      badge: 'Sale'
    },
    {
      id: 22,
      name: 'Stainless Steel Cookware Set',
      slug: 'stainless-steel-cookware-set',
      originalPrice: 9999,
      salePrice: 6699,
      discount: 33,
      image: '/images/products/cookware.jpg',
      rating: 4.5,
      reviews: 276,
      badge: 'Sale'
    },
    {
      id: 23,
      name: 'Luxury Sofa 3-Seater',
      slug: 'luxury-sofa-3-seater',
      originalPrice: 9999,
      salePrice: 7199,
      discount: 28,
      image: '/images/products/sofa.jpg',
      rating: 4.8,
      reviews: 123,
      badge: 'Sale'
    },
    {
      id: 24,
      name: 'Smart Home Security System',
      slug: 'smart-home-security-system',
      originalPrice: 9999,
      salePrice: 6999,
      discount: 30,
      image: '/images/products/security-system.jpg',
      rating: 4.7,
      reviews: 189,
      badge: 'Sale'
    }
  ]
};

interface ProductVariant {
  id: string;
  name: string;
  price: number;
  stock: number;
  sku: string;
  attributes?: Record<string, string>;
}

interface ProductReview {
  id: string;
  userName: string;
  userAvatar?: string;
  rating: number;
  title: string;
  comment: string;
  date: string;
  verified: boolean;
  helpful: number;
}

// Use SharedProduct interface from shared-products.ts
type Product = SharedProduct;

// Note: generateMockReviews is now imported from shared-products.ts

// Convert deal to product format
const convertDealToProduct = (deal: any, categoryName: string, subcategoryName: string): Product => {
  return {
    id: deal.id.toString(),
    name: deal.name,
    slug: deal.slug,
    description: `Premium quality ${deal.name.toLowerCase()} with excellent features and modern design. Perfect for everyday use with outstanding performance and reliability.`,
    images: [
      { src: deal.image, alt: deal.name },
      { src: deal.image, alt: `${deal.name} - View 2` },
      { src: deal.image, alt: `${deal.name} - View 3` },
    ],
    price: deal.salePrice,
    originalPrice: deal.originalPrice,
    discount: deal.discount,
    category: categoryName,
    subcategory: subcategoryName,
    brand: deal.name.includes('Designer') ? 'Designer Collection' :
           deal.name.includes('Premium') ? 'Premium Brand' :
           deal.name.includes('Luxury') ? 'Luxury Line' : 'Brand Collection',
    rating: deal.rating,
    reviewCount: deal.reviewCount || deal.reviews,
    variants: [
      {
        id: 'default',
        name: 'Default',
        price: deal.salePrice,
        stock: 50,
        sku: `${deal.slug.toUpperCase()}-001`,
      },
    ],
    features: [
      'High Quality Materials',
      'Modern Design',
      'Excellent Performance',
      'Durable Construction',
      'Easy to Use',
    ],
    specifications: {
      'Brand': deal.name.includes('Designer') ? 'Designer Collection' : 'Premium Brand',
      'Category': categoryName,
      'Subcategory': subcategoryName,
      'Rating': `${deal.rating}/5`,
      'Reviews': deal.reviewCount?.toString() || deal.reviews?.toString() || '0',
    },
    reviews: generateMockReviews(deal.name, deal.reviewCount || deal.reviews || 0),
    isWishlisted: false,
    inStock: true,
    badge: deal.badge,
  };
};

export default function ProductDetailPage() {
  const params = useParams();
  const searchParams = useSearchParams();
  const router = useRouter();
  const { tenantId } = useTenant();
  const { addItem, updateQuantity: updateCartQuantity } = useCart();
  const { addItem: addToZustandCart, updateQuantity: updateZustandQuantity } = useHydratedCartStore();

  // Use direct Zustand selectors for reactive cart state
  const zustandItems = useCartStore((state) => state.items);
  const zustandIsInCart = useCartStore((state) => state.isInCart);
  const zustandGetItemQuantity = useCartStore((state) => state.getItemQuantity);

  const categoryName = params.categoryName as string;
  const subcategoryName = params.subcategoryName as string;
  const productName = searchParams.get('product-name');

  const [selectedVariant, setSelectedVariant] = useState<ProductVariant | null>(null);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [quantity, setQuantity] = useState(1);
  const [isWishlisted, setIsWishlisted] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isMounted, setIsMounted] = useState(false);

  // Find the product from shared data
  const product = useMemo(() => {
    if (!productName || !categoryName || !subcategoryName) return null;

    // First try to find in the specific category/subcategory
    const foundProduct = findProductBySlug(categoryName, subcategoryName, productName);

    if (!foundProduct) {
      // If not found, try to find in deals data for backward compatibility
      if (tenantId) {
        const tenantDeals = topDealsProductsByTenant[tenantId] || [];
        const deal = tenantDeals.find(d => d.slug === productName);

        if (deal) {
          return convertDealToProduct(deal, categoryName, subcategoryName);
        }
      }
      return null;
    }

    return foundProduct;
  }, [productName, categoryName, subcategoryName, tenantId]);

  // Check if product is already in cart (only after hydration)
  const isProductInCart = useMemo(() => {
    if (!isMounted || !product || !selectedVariant) return false;
    return zustandIsInCart(product.id, selectedVariant);
  }, [isMounted, product, selectedVariant, zustandIsInCart]);

  // Get current quantity in cart (only after hydration)
  const cartQuantity = useMemo(() => {
    if (!isMounted || !product || !selectedVariant) return 0;
    return zustandGetItemQuantity(product.id, selectedVariant);
  }, [isMounted, product, selectedVariant, zustandGetItemQuantity]);

  // Set mounted state after hydration
  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Set default variant when product loads
  useEffect(() => {
    if (product && product.variants.length > 0) {
      setSelectedVariant(product.variants[0]);
    }
  }, [product]);

  // Sync quantity with cart when product is already in cart
  useEffect(() => {
    if (isProductInCart && cartQuantity > 0) {
      setQuantity(cartQuantity);
    } else if (!isProductInCart) {
      // Reset quantity to 1 when product is removed from cart
      setQuantity(1);
    }
  }, [isProductInCart, cartQuantity]);

  // Generate breadcrumbs
  const breadcrumbItems = useMemo(() => {
    const items = [
      { label: 'Home', href: '/' },
      { label: categoryName.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase()), href: `/categories/${categoryName}` },
      { label: subcategoryName.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase()), href: `/categories/${categoryName}?subcategory=${subcategoryName}` },
    ];

    if (product) {
      items.push({
        label: product.name,
        href: `/product/${categoryName}/${subcategoryName}?product-name=${productName}`,
        active: true,
      });
    }

    return items;
  }, [categoryName, subcategoryName, productName, product]);

  const handleAddToCart = async () => {
    if (!product || !selectedVariant || isProductInCart) return;

    setIsLoading(true);

    try {
      const cartItem = {
        productId: product.id,
        name: `${product.name} - ${selectedVariant.name}`,
        price: selectedVariant.price,
        image: product.images[0]?.src || '/images/placeholder-product.jpg',
        variant: {
          id: selectedVariant.id,
          name: selectedVariant.name,
          sku: selectedVariant.sku,
        },
        maxQuantity: selectedVariant.stock,
        sellerId: 'ondc-seller',
        sellerName: product.brand,
      };

      // Add to both cart systems for compatibility
      addItem(cartItem, quantity);
      addToZustandCart(cartItem, quantity);

      console.log('Successfully added to cart:', {
        product: product.name,
        variant: selectedVariant.name,
        quantity,
        price: selectedVariant.price,
      });

      // Show success toast
      toast.success(`Added ${quantity} ${product.name} to cart!`, {
        icon: '🛒',
        style: {
          borderRadius: '10px',
          background: '#10b981',
          color: '#fff',
        },
      });
    } catch (error) {
      console.error('Failed to add to cart:', error);

      // Show error toast
      toast.error('Failed to add item to cart. Please try again.', {
        icon: '❌',
        style: {
          borderRadius: '10px',
          background: '#ef4444',
          color: '#fff',
        },
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleQuantityChange = (newQuantity: number) => {
    if (selectedVariant && newQuantity >= 1 && newQuantity <= selectedVariant.stock) {
      setQuantity(newQuantity);

      // If product is already in cart, update cart quantity immediately
      if (isProductInCart) {
        const itemId = `${product?.id}-${selectedVariant.id}`;

        // Update both cart systems silently
        try {
          updateZustandQuantity(itemId, newQuantity);
          updateCartQuantity(itemId, newQuantity);

          // Show a subtle toast for quantity update
          toast.success(`Cart updated: ${newQuantity} ${product?.name}`, {
            icon: '🔄',
            duration: 2000,
            style: {
              borderRadius: '10px',
              background: '#6b7280',
              color: '#fff',
              fontSize: '14px',
            },
          });
        } catch (error) {
          console.error('Failed to update quantity:', error);
          toast.error('Failed to update cart quantity', {
            icon: '❌',
            duration: 2000,
            style: {
              borderRadius: '10px',
              background: '#ef4444',
              color: '#fff',
              fontSize: '14px',
            },
          });
        }
      }
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(price);
  };

  if (!product) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Product Not Found</h1>
          <p className="text-gray-600 mb-6">The product you're looking for doesn't exist.</p>
          <Link
            href="/deals"
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <ArrowLeftIcon className="h-4 w-4 mr-2" />
            Back to Deals
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Breadcrumbs */}
        <Breadcrumbs items={breadcrumbItems} className="mb-6" />

        <div className="bg-white rounded-xl shadow-sm overflow-hidden">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 p-8">
            {/* Product Images */}
            <div className="space-y-4">
              {/* Main Image */}
              <div className="aspect-square bg-gray-100 rounded-lg overflow-hidden">
                <Image
                  src={product.images[selectedImageIndex]?.src || product.images[0]?.src}
                  alt={product.images[selectedImageIndex]?.alt || product.name}
                  width={600}
                  height={600}
                  className="w-full h-full object-cover"
                />
              </div>

              {/* Thumbnail Images */}
              {product.images.length > 1 && (
                <div className="flex space-x-2">
                  {product.images.map((image, index) => (
                    <button
                      key={index}
                      onClick={() => setSelectedImageIndex(index)}
                      className={`w-20 h-20 rounded-lg overflow-hidden border-2 transition-colors ${
                        selectedImageIndex === index
                          ? 'border-blue-500'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      <Image
                        src={image.src}
                        alt={image.alt || `${product.name} thumbnail ${index + 1}`}
                        width={80}
                        height={80}
                        className="w-full h-full object-cover"
                      />
                    </button>
                  ))}
                </div>
              )}
            </div>

            {/* Product Info */}
            <div className="space-y-6">
              {/* Product Title and Badge */}
              <div>
                {product.badge && (
                  <span className="inline-block bg-red-100 text-red-800 text-sm font-medium px-3 py-1 rounded-full mb-2">
                    {product.badge}
                  </span>
                )}
                <h1 className="text-3xl font-bold text-gray-900">{product.name}</h1>
                <p className="text-lg text-gray-600 mt-2">{product.brand}</p>
              </div>

              {/* Rating */}
              <div className="flex items-center space-x-2">
                <div className="flex items-center">
                  {[...Array(5)].map((_, i) => (
                    <StarIcon
                      key={i}
                      className={`h-5 w-5 ${
                        i < Math.floor(product.rating)
                          ? 'text-yellow-400 fill-current'
                          : 'text-gray-300'
                      }`}
                    />
                  ))}
                </div>
                <span className="text-sm text-gray-600">
                  {product.rating} ({product.reviewCount} reviews)
                </span>
              </div>

              {/* Price */}
              <div className="space-y-2">
                <div className="flex items-baseline space-x-3">
                  <span className="text-3xl font-bold text-gray-900">
                    {formatPrice(product.price)}
                  </span>
                  {product.originalPrice && product.originalPrice > product.price && (
                    <>
                      <span className="text-xl text-gray-500 line-through">
                        {formatPrice(product.originalPrice)}
                      </span>
                      <span className="text-lg font-medium text-green-600">
                        {product.discount}% off
                      </span>
                    </>
                  )}
                </div>
                {product.originalPrice && product.originalPrice > product.price && (
                  <p className="text-sm text-green-600">
                    You save {formatPrice(product.originalPrice - product.price)}
                  </p>
                )}
              </div>

              {/* Description */}
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">Description</h3>
                <p className="text-gray-600">{product.description}</p>
              </div>

              {/* Quantity Selector */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Quantity
                </label>
                <div className="flex items-center space-x-3">
                  <button
                    onClick={() => handleQuantityChange(quantity - 1)}
                    disabled={quantity <= 1}
                    className="p-2 border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <MinusIcon className="h-4 w-4" />
                  </button>
                  <span className="text-lg font-medium px-4">{quantity}</span>
                  <button
                    onClick={() => handleQuantityChange(quantity + 1)}
                    disabled={!selectedVariant || quantity >= selectedVariant.stock}
                    className="p-2 border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <PlusIcon className="h-4 w-4" />
                  </button>
                  {selectedVariant && (
                    <span className="text-sm text-gray-500 ml-4">
                      {selectedVariant.stock} available
                    </span>
                  )}
                </div>
              </div>

              {/* Action Buttons */}
              <div className="space-y-4">
                <button
                  onClick={handleAddToCart}
                  disabled={isLoading || !selectedVariant || !product.inStock || isProductInCart}
                  className={`w-full py-3 px-6 rounded-lg font-medium transition-colors flex items-center justify-center ${
                    isProductInCart
                      ? 'bg-green-600 text-white cursor-not-allowed'
                      : 'bg-blue-600 text-white hover:bg-blue-700'
                  } disabled:opacity-50 disabled:cursor-not-allowed`}
                >
                  {isLoading ? (
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                  ) : isProductInCart ? (
                    <>
                      <ShoppingCartIcon className="h-5 w-5 mr-2" />
                      Added to Cart ({cartQuantity})
                    </>
                  ) : (
                    <>
                      <ShoppingCartIcon className="h-5 w-5 mr-2" />
                      Add to Cart
                    </>
                  )}
                </button>

                <div className="flex space-x-4">
                  <button
                    onClick={() => setIsWishlisted(!isWishlisted)}
                    className="flex-1 border border-gray-300 text-gray-700 py-3 px-6 rounded-lg font-medium hover:bg-gray-50 transition-colors flex items-center justify-center"
                  >
                    {isWishlisted ? (
                      <HeartSolidIcon className="h-5 w-5 mr-2 text-red-500" />
                    ) : (
                      <HeartIcon className="h-5 w-5 mr-2" />
                    )}
                    Wishlist
                  </button>
                  <button className="flex-1 border border-gray-300 text-gray-700 py-3 px-6 rounded-lg font-medium hover:bg-gray-50 transition-colors flex items-center justify-center">
                    <ShareIcon className="h-5 w-5 mr-2" />
                    Share
                  </button>
                </div>
              </div>

              {/* Features */}
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-3">Key Features</h3>
                <ul className="space-y-2">
                  {product.features.map((feature, index) => (
                    <li key={index} className="flex items-center text-gray-600">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mr-3"></div>
                      {feature}
                    </li>
                  ))}
                </ul>
              </div>

              {/* Trust Badges */}
              <div className="border-t pt-6">
                <div className="grid grid-cols-3 gap-4 text-center">
                  <div className="flex flex-col items-center">
                    <TruckIcon className="h-8 w-8 text-blue-600 mb-2" />
                    <span className="text-sm text-gray-600">Free Shipping</span>
                  </div>
                  <div className="flex flex-col items-center">
                    <ArrowPathIcon className="h-8 w-8 text-blue-600 mb-2" />
                    <span className="text-sm text-gray-600">Easy Returns</span>
                  </div>
                  <div className="flex flex-col items-center">
                    <ShieldCheckIcon className="h-8 w-8 text-blue-600 mb-2" />
                    <span className="text-sm text-gray-600">Secure Payment</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Product Specifications */}
          <div className="border-t bg-gray-50 p-8">
            <h3 className="text-xl font-bold text-gray-900 mb-6">Specifications</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {Object.entries(product.specifications).map(([key, value]) => (
                <div key={key} className="flex justify-between py-2 border-b border-gray-200">
                  <span className="font-medium text-gray-700">{key}</span>
                  <span className="text-gray-600">{value}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Customer Reviews */}
          <div className="border-t bg-white p-8">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-bold text-gray-900">Customer Reviews</h3>
              <div className="flex items-center space-x-2">
                <div className="flex items-center">
                  {[...Array(5)].map((_, i) => (
                    <StarIcon
                      key={i}
                      className={`h-5 w-5 ${
                        i < Math.floor(product.rating)
                          ? 'text-yellow-400 fill-current'
                          : 'text-gray-300'
                      }`}
                    />
                  ))}
                </div>
                <span className="text-sm text-gray-600">
                  {product.rating} out of 5 ({product.reviewCount} reviews)
                </span>
              </div>
            </div>

            {/* Reviews List */}
            <div className="space-y-6">
              {product.reviews.slice(0, 5).map((review) => (
                <div key={review.id} className="border-b border-gray-200 pb-6 last:border-b-0">
                  <div className="flex items-start space-x-4">
                    <div className="flex-shrink-0">
                      <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                        <span className="text-blue-600 font-medium text-sm">
                          {review.userName.charAt(0)}
                        </span>
                      </div>
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between mb-2">
                        <div>
                          <h4 className="font-medium text-gray-900">{review.userName}</h4>
                          <div className="flex items-center space-x-2 mt-1">
                            <div className="flex items-center">
                              {[...Array(5)].map((_, i) => (
                                <StarIcon
                                  key={i}
                                  className={`h-4 w-4 ${
                                    i < review.rating
                                      ? 'text-yellow-400 fill-current'
                                      : 'text-gray-300'
                                  }`}
                                />
                              ))}
                            </div>
                            {review.verified && (
                              <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">
                                Verified Purchase
                              </span>
                            )}
                          </div>
                        </div>
                        <span className="text-sm text-gray-500">{review.date}</span>
                      </div>
                      <h5 className="font-medium text-gray-900 mb-2">{review.title}</h5>
                      <p className="text-gray-600 mb-3">{review.comment}</p>
                      <div className="flex items-center space-x-4 text-sm">
                        <button className="text-gray-500 hover:text-gray-700 flex items-center">
                          <span>Helpful ({review.helpful})</span>
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Show More Reviews Button */}
            {product.reviews.length > 5 && (
              <div className="mt-6 text-center">
                <button className="text-blue-600 hover:text-blue-700 font-medium">
                  View All {product.reviewCount} Reviews
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
