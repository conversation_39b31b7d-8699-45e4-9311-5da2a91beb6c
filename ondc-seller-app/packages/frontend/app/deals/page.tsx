'use client';

import React, { useState, useEffect, Suspense } from 'react';
import { useSearchParams } from 'next/navigation';
import { useTenant } from '@/contexts/TenantContext';
import ProductListingPage, { Product, ProductListingConfig } from '@/components/common/ProductListingPage';
import {
  getFeaturedProductsByTenant,
  getTopDealsByTenant,
  getHotPicksByTenant
} from '@/data/homepage-mock-data';

function DealsPageContent() {
  const searchParams = useSearchParams();
  const dealType = searchParams?.get('type') || 'top-deals';
  const { tenantId } = useTenant();

  const [products, setProducts] = useState<Product[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadProducts();
  }, [dealType, tenantId]);

  const loadProducts = async () => {
    setIsLoading(true);
    try {
      let rawProducts: any[] = [];

      switch (dealType) {
        case 'featured-products':
          // Use the same data source as FeaturedProducts component
          const featuredProducts = getFeaturedProductsByTenant(tenantId || '20');
          rawProducts = featuredProducts.map(product => ({
            id: parseInt(product.id.replace('featured-', '')),
            name: product.name,
            slug: product.slug,
            salePrice: product.price / 100, // Convert from paise to rupees
            originalPrice: product.originalPrice ? product.originalPrice / 100 : product.price / 100,
            image: product.image,
            rating: product.rating,
            reviewCount: product.reviewCount,
            category: product.category,
            isWishlisted: false,
            badge: product.badge,
            brand: 'Featured Brand',
            inStock: true
          }));
          break;

        case 'hot-picks':
          // Use the same data source as HotPicks component
          const hotPicksProducts = getHotPicksByTenant(tenantId || '20');
          rawProducts = hotPicksProducts.map(product => ({
            id: product.id,
            name: product.name,
            slug: product.slug,
            salePrice: product.salePrice || product.price,
            originalPrice: product.salePrice ? product.price : product.price,
            image: product.image,
            rating: product.rating,
            reviewCount: product.reviews,
            category: 'Hot Picks',
            isWishlisted: false,
            badge: product.badge,
            brand: 'Trending Brand',
            inStock: true
          }));
          break;

        default: // top-deals
          // Use the same data source as TopDeals component
          const topDealsProducts = getTopDealsByTenant(tenantId || '20');
          rawProducts = topDealsProducts.map(product => ({
            id: product.id,
            name: product.name,
            slug: product.slug,
            salePrice: product.salePrice,
            originalPrice: product.originalPrice,
            image: product.image,
            rating: product.rating,
            reviewCount: product.reviews,
            category: 'Top Deals',
            isWishlisted: false,
            badge: product.badge,
            brand: 'Deal Brand',
            inStock: true
          }));
          break;
      }



      setProducts(rawProducts);
    } catch (error) {
      console.error('Error loading products:', error);
      setProducts([]);
    } finally {
      setIsLoading(false);
    }
  };

  const getPageConfig = (): ProductListingConfig => {
    switch (dealType) {
      case 'featured-products':
        return {
          title: 'Featured Products',
          subtitle: 'Handpicked products just for you',
          showDiscountBadge: true,
          showSaleBadge: true,
          showPriceComparison: true,
          showSavingsAmount: true,
          buttonText: 'Add to Cart',
          buttonStyle: 'primary',
          breadcrumbs: [
            { label: 'Home', href: '/' },
            { label: 'Deals', href: '/deals' },
            { label: 'Featured Products' }
          ],
        };
      case 'hot-picks':
        return {
          title: 'Hot Picks',
          subtitle: 'Trending products everyone loves',
          showDiscountBadge: true,
          showSaleBadge: true,
          showPriceComparison: true,
          showSavingsAmount: true,
          buttonText: 'Add to Cart',
          buttonStyle: 'primary',
          breadcrumbs: [
            { label: 'Home', href: '/' },
            { label: 'Deals', href: '/deals' },
            { label: 'Hot Picks' }
          ],
        };
      default:
        return {
          title: 'Top Deals',
          subtitle: 'Best offers with up to 70% off',
          showDiscountBadge: true,
          showSaleBadge: true,
          showPriceComparison: true,
          showSavingsAmount: true,
          buttonText: 'Add to Cart',
          buttonStyle: 'primary',
          breadcrumbs: [
            { label: 'Home', href: '/' },
            { label: 'Deals', href: '/deals' },
            { label: 'Top Deals' }
          ],
        };
    }
  };

  return (
    <ProductListingPage
      products={products}
      config={getPageConfig()}
      isLoading={isLoading}
    />
  );
}

export default function DealsPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <DealsPageContent />
    </Suspense>
  );
}
