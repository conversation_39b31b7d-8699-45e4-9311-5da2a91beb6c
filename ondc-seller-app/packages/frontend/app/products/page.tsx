'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import Breadcrumbs from '@/components/Breadcrumbs';
import Image from '@/components/ui/Image';
import { useStoreProducts } from '@/lib/api/hooks';
import { useTenant } from '@/contexts/TenantContext';
import { Product } from '@/lib/api/types';
import {
  ExclamationTriangleIcon,
  ArrowPathIcon,
  CubeIcon
} from '@heroicons/react/24/outline';

// Using Product type from API types

// Define price ranges
const priceRanges = [
  { label: 'All Prices', value: 'all' },
  { label: 'Under ₹50', value: 'under-50' },
  { label: '₹50 - ₹100', value: '50-100' },
  { label: '₹100 - ₹200', value: '100-200' },
  { label: 'Over ₹200', value: 'over-200' },
];

// Define sort options
const sortOptions = [
  { label: 'Featured', value: 'featured' },
  { label: 'Price: Low to High', value: 'price-asc' },
  { label: 'Price: High to Low', value: 'price-desc' },
  { label: 'Rating', value: 'rating' },
];

// Define product categories
const productCategories = [
  'Electronics',
  'Fashion',
  'Home & Living',
  'Beauty',
  'Sports',
  'Books',
] as const;

type Category = (typeof productCategories)[number];

const ProductsPage = () => {
  const { selectedTenant } = useTenant();
  const { data: productsData, loading, error, refetch } = useStoreProducts();
  const [sortBy, setSortBy] = useState('featured');
  const [priceRange, setPriceRange] = useState('all');
  const [selectedCategories, setSelectedCategories] = useState<Category[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(12);
  const [showFilters, setShowFilters] = useState(false);

  // Get products from API response
  const products = productsData?.products || [];

  // Refetch products when tenant changes
  useEffect(() => {
    if (selectedTenant) {
      refetch();
    }
  }, [selectedTenant, refetch]);

  const formatPrice = (amount?: number, currency?: string) => {
    if (amount === undefined) {
      return 'Price not available';
    }
    try {
      return new Intl.NumberFormat('en-IN', {
        style: 'currency',
        currency: (currency || 'INR').toUpperCase(),
      }).format(amount);
    } catch (error) {
      console.error('Error formatting price:', error);
      return `₹${amount.toFixed(2)}`;
    }
  };

  // Filter products - API products don't have price structure, so we'll use basic filtering
  const filteredProducts = products.filter(product => {
    // For now, just return all products since API doesn't provide price info
    // TODO: Add price filtering when API provides price data
    return true;
  });

  // Sort products - basic sorting by title for now
  const sortedProducts = [...filteredProducts].sort((a: Product, b: Product) => {
    if (sortBy === 'featured') {
      return 0;
    }
    // Sort by title since API products don't have price/rating
    return a.title.localeCompare(b.title);
  });

  if (loading) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Breadcrumbs
          items={[
            { label: 'Home', href: '/' },
            { label: 'All Products', href: '/products', active: true },
          ]}
        />
        <div className="text-center py-10">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-600"></div>
          <p className="mt-2 text-gray-600">Loading products...</p>
          {selectedTenant && (
            <p className="text-sm text-gray-500">Fetching products for {selectedTenant.name}</p>
          )}
        </div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Breadcrumbs
          items={[
            { label: 'Home', href: '/' },
            { label: 'All Products', href: '/products', active: true },
          ]}
        />
        <div className="bg-red-50 border border-red-200 rounded-lg p-6">
          <div className="flex">
            <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">
                Failed to load products
              </h3>
              <div className="mt-2 text-sm text-red-700">
                <p>{error.message || error.error}</p>
              </div>
              <div className="mt-4">
                <button
                  onClick={refetch}
                  className="bg-red-100 px-3 py-2 rounded-md text-sm font-medium text-red-800 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 flex items-center"
                >
                  <ArrowPathIcon className="h-4 w-4 mr-2" />
                  Try Again
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Breadcrumb items
  const breadcrumbItems = [
    { label: 'Home', href: '/' },
    { label: 'All Products', href: '/products', active: true },
  ];

  // Show empty state if no products
  if (products.length === 0 && !loading && !error) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Breadcrumbs items={breadcrumbItems} className="mb-6" />
        <div className="text-center py-12">
          <CubeIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No products available</h3>
          <p className="mt-1 text-sm text-gray-500">
            {selectedTenant ? `No products found for ${selectedTenant.name}` : 'No products found'}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <Breadcrumbs items={breadcrumbItems} className="mb-6" />

      {/* Tenant Info */}
      {selectedTenant && productsData && (
        <div className="mb-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-lg font-medium text-blue-900">
                {selectedTenant.name} Products
              </h2>
              <p className="text-sm text-blue-700">
                Showing {productsData.filtered_for_tenant} of {productsData.total_products_in_system} total products
              </p>
            </div>
            <div className="text-right">
              <p className="text-sm text-blue-600">Store: {productsData.store.name}</p>
              <p className="text-xs text-blue-500">{productsData.store.domain}</p>
            </div>
          </div>
        </div>
      )}

      <div className="flex flex-col md:flex-row gap-8">
        {/* Filters Sidebar */}
        <div className="w-full md:w-64 space-y-8">
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Categories</h3>
            <div className="space-y-2">
              {productCategories.map(category => (
                <label key={category} className="flex items-center">
                  <input
                    type="checkbox"
                    checked={selectedCategories.includes(category)}
                    onChange={() => {
                      if (selectedCategories.includes(category)) {
                        setSelectedCategories(prev => prev.filter(c => c !== category));
                      } else {
                        setSelectedCategories(prev => [...prev, category]);
                      }
                    }}
                    className="h-4 w-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500"
                  />
                  <span className="ml-2 text-gray-600">{category}</span>
                </label>
              ))}
            </div>
          </div>

          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Price Range</h3>
            <div className="space-y-2">
              {priceRanges.map(range => (
                <label key={range.value} className="flex items-center">
                  <input
                    type="radio"
                    name="price-range"
                    value={range.value}
                    checked={priceRange === range.value}
                    onChange={e => setPriceRange(e.target.value)}
                    className="h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                  />
                  <span className="ml-2 text-gray-600">{range.label}</span>
                </label>
              ))}
            </div>
          </div>
        </div>

        {/* Products Grid */}
        <div className="flex-1">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-gray-900">
              All Products ({sortedProducts.length})
            </h2>
            <select
              value={sortBy}
              onChange={e => setSortBy(e.target.value)}
              className="rounded-md border-gray-300 py-2 pl-3 pr-10 text-base focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            >
              {sortOptions.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {sortedProducts.map(product => (
              <div
                key={product.id}
                className="group relative bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-200 h-96 flex flex-col"
              >
                {/* Fixed height image container */}
                <div className="relative h-48 w-full overflow-hidden">
                  {product.thumbnail ? (
                    <Image
                      src={product.thumbnail}
                      alt={product.title}
                      width={400}
                      height={300}
                      className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
                      fallback="/images/placeholder.jpg"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center bg-gray-100">
                      <CubeIcon className="h-12 w-12 text-gray-400" />
                    </div>
                  )}
                  {/* Status badge */}
                  <div className="absolute top-2 left-2">
                    <span className={`inline-block text-xs px-2 py-1 rounded-full font-medium ${
                      product.status === 'published'
                        ? 'bg-green-100 text-green-800'
                        : 'bg-yellow-100 text-yellow-800'
                    }`}>
                      {product.status}
                    </span>
                  </div>
                </div>

                {/* Content container with flex-grow */}
                <div className="p-4 flex-1 flex flex-col">
                  {/* Product title - fixed 2 lines */}
                  <div className="mb-2">
                    <h3
                      className="text-lg font-semibold text-gray-900 line-clamp-2 h-14 leading-7"
                      title={product.title}
                    >
                      {product.title}
                    </h3>
                  </div>

                  {/* Description - fixed 2 lines */}
                  <div className="mb-3">
                    <p
                      className="text-sm text-gray-600 line-clamp-2 h-10 leading-5"
                      title={product.description || ''}
                    >
                      {product.description ||
                        'High-quality product with excellent features and reliable performance.'}
                    </p>
                  </div>

                  {/* Product details */}
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center">
                      <span className="text-xs text-gray-500">ID:</span>
                      <span className="ml-1 text-xs font-medium text-gray-900 truncate max-w-20">
                        {product.id}
                      </span>
                    </div>
                    {product.handle && (
                      <div className="text-xs text-gray-500">
                        Handle: {product.handle}
                      </div>
                    )}
                  </div>

                  {/* Dimensions if available */}
                  {(product.weight || product.length || product.height || product.width) && (
                    <div className="mb-3">
                      <div className="text-xs text-gray-500">
                        {product.weight && `Weight: ${product.weight}g `}
                        {product.length && product.height && product.width &&
                          `Dimensions: ${product.length}×${product.width}×${product.height}cm`}
                      </div>
                    </div>
                  )}

                  {/* Actions - pushed to bottom */}
                  <div className="mt-auto">
                    <div className="space-y-2">
                      <Link
                        href={`/products/${product.id}`}
                        className="block w-full text-center bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors duration-200 text-sm font-medium"
                      >
                        View Details
                      </Link>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProductsPage;
