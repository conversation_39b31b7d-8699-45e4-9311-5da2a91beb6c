'use client';

import React, { useState, useEffect, Suspense } from 'react';
import { useTenant } from '@/contexts/TenantContext';
import ProductListingPage, { Product, ProductListingConfig } from '@/components/common/ProductListingPage';
import { useStoreProducts } from '@/lib/api/hooks';
import { Product as APIProduct } from '@/lib/api/types';

function ProductsPageContent() {
  const { tenantId } = useTenant();
  const { data: productsData, loading, error, refetch } = useStoreProducts();
  const [products, setProducts] = useState<Product[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadProducts();
  }, [productsData, tenantId]);

  // Refetch products when tenant changes
  useEffect(() => {
    if (tenantId) {
      refetch();
    }
  }, [tenantId, refetch]);

  const loadProducts = async () => {
    setIsLoading(true);
    try {
      if (productsData?.products) {
        // Convert API products to ProductListingPage format
        const convertedProducts: Product[] = productsData.products.map((apiProduct: APIProduct, index: number) => ({
          id: apiProduct.id, // Use original product ID instead of converting to number
          name: apiProduct.title,
          slug: apiProduct.handle || apiProduct.title.toLowerCase().replace(/\s+/g, '-'),
          originalPrice: 299, // Default price since API doesn't provide pricing
          salePrice: 199, // Default sale price
          discount: 33, // Default discount
          image: apiProduct.thumbnail || `https://picsum.photos/400/400?random=${apiProduct.id}`,
          category: 'Products',
          subcategory: 'General',
          tenantId: tenantId || 'default',
          rating: 4.5, // Default rating
          reviewCount: Math.floor(Math.random() * 500) + 50, // Random review count
          badge: apiProduct.status === 'published' ? 'Available' : 'Draft',
          brand: 'API Brand',
          inStock: apiProduct.status === 'published',
          tags: apiProduct.tags?.map(tag => tag.value) || []
        }));

        setProducts(convertedProducts);
      } else {
        setProducts([]);
      }
    } catch (error) {
      console.error('Error loading products:', error);
      setProducts([]);
    } finally {
      setIsLoading(false);
    }
  };

  const getPageConfig = (): ProductListingConfig => {
    return {
      title: 'All Products',
      subtitle: 'Discover our complete product collection',
      showDiscountBadge: true,
      showSaleBadge: true,
      showPriceComparison: true,
      showSavingsAmount: true,
      buttonText: 'View Product',
      buttonStyle: 'primary',
      productUrlPattern: 'products', // Use products URL pattern
      breadcrumbs: [
        { label: 'Home', href: '/' },
        { label: 'Products' }
      ],
    };
  };

  return (
    <ProductListingPage
      products={products}
      config={getPageConfig()}
      isLoading={isLoading || loading}
    />
  );
}

export default function ProductsPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <ProductsPageContent />
    </Suspense>
  );
}


