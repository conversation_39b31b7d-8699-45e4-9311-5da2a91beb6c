'use client';

import React, { useEffect, useState } from 'react';
import MultiTenantHeroBanner from '@/components/homepage/MultiTenantHeroBanner';
import ApiProductsCarouselWrapper from '@/components/homepage/ApiProductsCarouselWrapper';
import TopDeals from '@/components/homepage/TopDeals';
import MultiTenantFeaturedProducts from '@/components/homepage/MultiTenantFeaturedProducts';
import HotPicks from '@/components/homepage/HotPicks';

export default function HomePage() {
  const [pageKey, setPageKey] = useState(0);
  const [authData, setAuthData] = useState<{
    user: any;
    isAuthenticated: boolean;
    totalItems: number;
  }>({
    user: null,
    isAuthenticated: false,
    totalItems: 0,
  });

  useEffect(() => {
    // Force re-render when component mounts
    console.log('HomePage component mounted/re-mounted');
    setPageKey(prev => prev + 1);
  }, []);

  // Listen for route changes and refresh banner
  useEffect(() => {
    const handleRouteChange = () => {
      console.log('Route changed, refreshing banner...');
      setPageKey(prev => prev + 1);
    };

    // Listen for browser navigation events
    window.addEventListener('popstate', handleRouteChange);

    return () => {
      window.removeEventListener('popstate', handleRouteChange);
    };
  }, []);

  // Note: Tenant switching is now handled by individual components

  return (
    <div className="min-h-screen bg-white">
      {/* Multi-Tenant Hero Banner Carousel */}
      <MultiTenantHeroBanner key={`hero-banner-${pageKey}`} />

      {/* API Products Carousel - Real-time data from backend */}
      <ApiProductsCarouselWrapper
        title="Latest Products"
        maxProducts={8}
      />

      {/* Top Deals Section */}
      <TopDeals />

      {/* Multi-Tenant Featured Products Section */}
      <MultiTenantFeaturedProducts key={`products-${pageKey}`} />

      {/* Hot Picks Section */}
      <HotPicks />

      {/* Welcome Message for Authenticated Users */}
      {authData.isAuthenticated && (
        <div className="bg-blue-50 border-l-4 border-blue-400 py-8">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex">
              <div className="ml-3">
                <p className="text-sm text-blue-700">
                  Welcome back, {authData.user?.name || 'User'}! You have {authData.totalItems}{' '}
                  item(s) in your cart.
                  {selectedTenant && (
                    <span className="ml-2">
                      Currently shopping at <strong>{selectedTenant.name}</strong>.
                    </span>
                  )}
                </p>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
