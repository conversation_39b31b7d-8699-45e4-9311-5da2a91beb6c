import { NextRequest, NextResponse } from 'next/server';
import { getCategories } from '@/lib/strapi-api';

// GET /api/admin/categories - List all categories with admin features
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search') || '';
    const status = searchParams.get('status') || '';
    const parentId = searchParams.get('parentId') || '';
    const sortBy = searchParams.get('sortBy') || 'sortOrder';
    const sortOrder = searchParams.get('sortOrder') || 'asc';

    console.log('🚀 Admin Categories API: Fetching categories from Strapi CMS...');
    console.log('📊 Parameters:', {
      page,
      limit,
      search,
      status,
      parentId,
      sortBy,
      sortOrder,
    });

    // Fetch categories from Strapi CMS
    const strapiResponse = await getCategories({
      page,
      pageSize: limit,
    });

    console.log('✅ Admin Categories API: Successfully fetched categories');
    console.log('📊 Categories count:', strapiResponse.data?.length || 0);

    // Transform Strapi data to admin format
    let filteredCategories = (strapiResponse.data || []).map(category => ({
      id: category.id.toString(),
      name: category.name,
      slug: category.slug,
      description: Array.isArray(category.description)
        ? category.description.map(p => p.children?.map(c => c.text).join(' ')).join(' ')
        : category.description || '',
      image: category.image?.url || '/images/categories/placeholder.jpg',
      status: category.featured ? 'active' : 'inactive',
      parentId: category.parent?.id?.toString() || null,
      sortOrder: category.id,
      productCount: 0, // This would need to be calculated from products
      metaTitle: category.name,
      metaDescription: Array.isArray(category.description)
        ? category.description.map(p => p.children?.map(c => c.text).join(' ')).join(' ')
        : category.description || '',
      createdAt: category.createdAt,
      updatedAt: category.updatedAt,
    }));

    // Apply search filter
    if (search) {
      filteredCategories = filteredCategories.filter(
        category =>
          category.name.toLowerCase().includes(search.toLowerCase()) ||
          category.description.toLowerCase().includes(search.toLowerCase()) ||
          category.slug.toLowerCase().includes(search.toLowerCase())
      );
    }

    // Apply status filter
    if (status) {
      filteredCategories = filteredCategories.filter(category => category.status === status);
    }

    // Apply parent filter
    if (parentId) {
      filteredCategories = filteredCategories.filter(category => category.parentId === parentId);
    }

    // Apply sorting
    filteredCategories.sort((a, b) => {
      let aValue = a[sortBy as keyof typeof a];
      let bValue = b[sortBy as keyof typeof b];

      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = (bValue as string).toLowerCase();
      }

      if (sortOrder === 'desc') {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      } else {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      }
    });

    // Apply pagination
    const offset = (page - 1) * limit;
    const paginatedCategories = filteredCategories.slice(offset, offset + limit);

    const apiResponse = {
      categories: paginatedCategories,
      pagination: {
        page,
        limit,
        total: filteredCategories.length,
        totalPages: Math.ceil(filteredCategories.length / limit),
        hasNext: offset + limit < filteredCategories.length,
        hasPrev: page > 1,
      },
      filters: {
        search,
        status,
        parentId,
        sortBy,
        sortOrder,
      },
    };

    console.log('[Admin Categories API] Returning:', {
      count: paginatedCategories.length,
      total: filteredCategories.length,
      page,
    });

    return NextResponse.json(apiResponse);
  } catch (error) {
    console.error('[Admin Categories API] Error:', error);
    return NextResponse.json(
      {
        error: 'Failed to fetch categories',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// POST /api/admin/categories - Create new category
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    console.log('[Admin Categories API] POST request:', body);

    // Validate required fields
    const requiredFields = ['name', 'slug'];
    for (const field of requiredFields) {
      if (!body[field]) {
        return NextResponse.json({ error: `Missing required field: ${field}` }, { status: 400 });
      }
    }

    // TODO: Implement Strapi category creation
    // For now, return a placeholder response
    const newCategory = {
      id: Date.now().toString(),
      name: body.name,
      slug: body.slug,
      description: body.description || '',
      image: body.image || '/images/categories/placeholder.jpg',
      status: body.status || 'active',
      parentId: body.parentId || null,
      sortOrder: body.sortOrder || 1,
      productCount: 0,
      metaTitle: body.metaTitle || body.name,
      metaDescription: body.metaDescription || body.description || '',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    // TODO: Implement actual Strapi category creation
    console.log('⚠️ Admin Categories API: Category creation not yet implemented with Strapi');

    console.log('[Admin Categories API] Created category:', newCategory.id);

    return NextResponse.json(
      { message: 'Category created successfully', category: newCategory },
      { status: 201 }
    );
  } catch (error) {
    console.error('[Admin Categories API] Create error:', error);
    return NextResponse.json(
      {
        error: 'Failed to create category',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
