'use client';

import React, { useState, useEffect, Suspense } from 'react';
import { useSearchParams } from 'next/navigation';
import { useTenant } from '@/contexts/TenantContext';
import ProductListingPage, { Product, ProductListingConfig } from '@/components/common/ProductListingPage';
import { getTrendingProductsByTenant } from '@/data/homepage-mock-data';

function TrendingPageContent() {
  const { tenantId } = useTenant();
  const [products, setProducts] = useState<Product[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadProducts();
  }, [tenantId]);

  const loadProducts = async () => {
    setIsLoading(true);
    try {
      // Get trending products from mock data
      const effectiveTenantId = String(tenantId || '20');
      console.log('Loading trending products for tenant:', { tenantId, effectiveTenantId, type: typeof effectiveTenantId });

      const trendingProducts = getTrendingProductsByTenant(effectiveTenantId);
      console.log('Found trending products:', trendingProducts.length, 'for tenant:', effectiveTenantId);

      const rawProducts = trendingProducts.map(product => ({
        id: product.id,
        name: product.name,
        slug: product.slug,
        salePrice: product.salePrice || product.price,
        originalPrice: product.price,
        image: product.image,
        rating: product.rating,
        reviewCount: product.reviews,
        category: 'Trending',
        isWishlisted: false,
        badge: product.badge,
        brand: 'Trending Brand',
        inStock: true
      }));

      setProducts(rawProducts);
    } catch (error) {
      console.error('Error loading trending products:', error);
      setProducts([]);
    } finally {
      setIsLoading(false);
    }
  };

  const config: ProductListingConfig = {
    title: 'Trending Products',
    subtitle: 'Hot & Trending',
    description: '🔥 Discover what\'s trending! These products are flying off our shelves.',
    showDiscountBadge: true,
    showSaleBadge: true,
    showPriceComparison: true,
    showSavingsAmount: true,
    buttonText: 'View Product',
    buttonStyle: 'bg-orange-600 hover:bg-orange-700 text-white',
    productUrlPattern: 'trending', // Use trending URL pattern
    breadcrumbs: [
      { label: 'Home', href: '/' },
      { label: 'Trending Products' }
    ],
    showFilters: true,
    showSort: true,
    showViewToggle: true
  };

  return (
    <ProductListingPage
      products={products}
      config={config}
      isLoading={isLoading}
    />
  );
}

export default function TrendingPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex justify-center items-center py-16">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-600"></div>
            <span className="ml-3 text-gray-600">Loading trending products...</span>
          </div>
        </div>
      </div>
    }>
      <TrendingPageContent />
    </Suspense>
  );
}
