// Multi-Tenant ONDC Seller App API Types
// Generated from OpenAPI specification

export type TenantId = 'tenant-electronics-001' | 'tenant-fashion-002' | 'default';

// Base API Response
export interface ApiResponse<T = any> {
  data?: T;
  error?: string;
  message?: string;
  tenant_id?: string;
}

// Tenant Configuration
export interface TenantConfig {
  id: string;
  name: string;
  settings: {
    currency: string;
    timezone: string;
    features: string[];
    ondcConfig: {
      participantId: string;
      subscriberId: string;
      bppId: string;
      domain: string;
      region: string;
    };
  };
}

// Store Information
export interface StoreInfo {
  id: string;
  name: string;
  domain: string;
  currency: string;
  timezone: string;
  region: string;
  features: string[];
  branding: {
    primaryColor: string;
    logo: string;
  };
  contact: {
    email: string;
    phone: string;
    address: {
      street: string;
      city: string;
      state: string;
      country: string;
      pincode: string;
    };
  };
  ondcConfig: {
    participantId: string;
    subscriberId: string;
    bppId: string;
    domain: string;
    region: string;
  };
}

// Product Types
export interface Product {
  id: string;
  title: string;
  description?: string;
  handle: string;
  status: string;
  thumbnail?: string;
  weight?: number;
  length?: number;
  height?: number;
  width?: number;
  hs_code?: string;
  origin_country?: string;
  material?: string;
}

export interface ProductDetails extends Product {
  store: {
    id: string;
    name: string;
    domain: string;
  };
  tenant_id: string;
  availability: string;
  price: {
    amount: number;
    currency: string;
  };
  shipping: {
    free_shipping: boolean;
    estimated_delivery: string;
    regions: string[];
  };
  warranty: string;
}

// Customer Types
export interface Customer {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
}

// API Request/Response Types
export interface TenantConfigResponse extends ApiResponse<TenantConfig> {
  tenant: TenantConfig;
  tenant_id: string;
}

export interface StoreInfoResponse extends ApiResponse<StoreInfo> {
  store: StoreInfo;
  tenant_id: string;
  api_version: string;
  multi_tenant: boolean;
  timestamp: string;
}

export interface StoreProductsResponse extends ApiResponse<Product[]> {
  store: {
    id: string;
    name: string;
    domain: string;
  };
  products: Product[];
  count: number;
  tenant_id: string;
  total_products_in_system: number;
  filtered_for_tenant: number;
  note: string;
}

export interface ProductDetailsResponse extends ApiResponse<ProductDetails> {
  product: ProductDetails;
  tenant_id: string;
  store: {
    id: string;
    name: string;
    domain: string;
  };
  api_version: string;
  multi_tenant: boolean;
}

export interface MultiTenantTestResponse extends ApiResponse {
  tenant: {
    id: string;
    name: string;
  };
  products: {
    count: number;
    items: Product[];
    note: string;
  };
  customers: {
    count: number;
    items: Customer[];
    note: string;
  };
  tenant_id: string;
  isolation_test: {
    api_products_returned: number;
    api_customers_returned: number;
    tenant_detected: string;
    query_method: string;
    note: string;
  };
}

// Authentication Types
export interface AuthTokenResponse {
  token: string;
  user: {
    id: string;
    email: string;
    first_name?: string;
    last_name?: string;
  };
}

// API Client Configuration
export interface ApiClientConfig {
  baseUrl: string;
  tenantId: TenantId;
  adminToken?: string;
  publishableApiKey?: string;
}

// Error Types
export interface ApiError {
  error: string;
  message?: string;
  tenant_id?: string;
  product_id?: string;
  note?: string;
}

// Customer Types
export interface Customer {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  phone?: string;
  has_account: boolean;
  tenant_id: string;
  addresses: Address[];
  orders: string[];
  created_at: string;
  updated_at: string;
}

export interface Address {
  id: string;
  first_name: string;
  last_name: string;
  address_1: string;
  address_2?: string;
  city: string;
  postal_code: string;
  country_code: string;
  phone?: string;
}

// Cart Types
export interface Cart {
  id: string;
  customer_id?: string;
  tenant_id: string;
  region_id: string;
  currency_code: string;
  items: CartItem[];
  subtotal: number;
  tax_total: number;
  total: number;
  created_at: string;
  updated_at: string;
}

export interface CartItem {
  id: string;
  cart_id: string;
  product_id: string;
  variant_id: string;
  title: string;
  description: string;
  thumbnail: string;
  quantity: number;
  unit_price: number;
  total: number;
  created_at: string;
  updated_at: string;
}

// Order Types
export interface Order {
  id: string;
  display_id: string;
  customer_id?: string;
  tenant_id: string;
  email: string;
  status: 'pending' | 'completed' | 'cancelled';
  fulfillment_status: 'not_fulfilled' | 'fulfilled' | 'partially_fulfilled';
  payment_status: 'awaiting' | 'captured' | 'refunded';
  currency_code: string;
  items: OrderItem[];
  shipping_address: Address;
  billing_address: Address;
  subtotal: number;
  tax_total: number;
  shipping_total: number;
  total: number;
  created_at: string;
  updated_at: string;
}

export interface OrderItem {
  id: string;
  order_id: string;
  product_id: string;
  variant_id: string;
  title: string;
  description: string;
  thumbnail: string;
  quantity: number;
  unit_price: number;
  total: number;
}

// Category Types
export interface Category {
  id: string;
  name: string;
  slug: string;
  description?: string;
  image?: string;
  parent_id?: string;
  parent?: Category;
  children?: Category[];
  product_count: number;
  status: 'active' | 'inactive';
  sort_order: number;
  tenant_id: string;
  seo?: {
    title?: string;
    description?: string;
    keywords?: string[];
  };
  metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;
}

// Request Headers
export interface RequestHeaders {
  'Content-Type'?: string;
  'Authorization'?: string;
  'x-tenant-id': string;
  'x-publishable-api-key'?: string;
}

// API Endpoints
export const API_ENDPOINTS = {
  // Authentication
  AUTH_LOGIN: '/auth/user/emailpass',
  
  // Admin Endpoints
  ADMIN_TENANT: '/admin/tenant',
  ADMIN_TEST_MULTI_TENANT: '/admin/test-multi-tenant',
  
  // Store Endpoints
  STORE_INFO: '/store/test-info',
  STORE_PRODUCTS: '/store/test-products',
  STORE_PRODUCT_DETAILS: '/store/test-products',

  // Customer Endpoints
  STORE_CUSTOMERS: '/store/customers',
  STORE_CUSTOMER_DETAILS: '/store/customers',

  // Cart Endpoints
  STORE_CARTS: '/store/carts',
  STORE_CART_DETAILS: '/store/carts',
  STORE_CART_LINE_ITEMS: '/store/carts',

  // Order Endpoints
  STORE_ORDERS: '/store/orders',
  STORE_ORDER_DETAILS: '/store/orders',

  // Category Endpoints
  STORE_CATEGORIES: '/store/categories',
  STORE_CATEGORY_DETAILS: '/store/categories',
} as const;

// Customer Response Types
export interface CustomersResponse {
  customers: Customer[];
  count: number;
  offset: number;
  limit: number;
  tenant_id: string;
  timestamp: string;
}

export interface CustomerResponse {
  customer: Customer;
  tenant_id: string;
  timestamp: string;
}

// Cart Response Types
export interface CartsResponse {
  carts: Cart[];
  count: number;
  offset: number;
  limit: number;
  tenant_id: string;
  timestamp: string;
}

export interface CartResponse {
  cart: Cart;
  tenant_id?: string;
  message?: string;
  timestamp: string;
}

// Order Response Types
export interface OrdersResponse {
  orders: Order[];
  count: number;
  offset: number;
  limit: number;
  tenant_id: string;
  timestamp: string;
}

export interface OrderResponse {
  order: Order;
  tenant_id?: string;
  message?: string;
  timestamp: string;
}

// Category Response Types
export interface CategoriesResponse {
  categories: Category[];
  count: number;
  offset: number;
  limit: number;
  tenant_id: string;
  timestamp: string;
}

export interface CategoryResponse {
  category: Category;
  tenant_id?: string;
  message?: string;
  timestamp: string;
}

// Default Configuration
export const DEFAULT_CONFIG = {
  BASE_URL: 'http://localhost:9000',
  PUBLISHABLE_API_KEY: 'pk_51719ff15f2d615335059dc2bad507d3446466d7d6e2af5c777cf07d7ac53af0',
  TENANT_IDS: {
    ELECTRONICS: 'tenant-electronics-001' as TenantId,
    FASHION: 'tenant-fashion-002' as TenantId,
    DEFAULT: 'default' as TenantId,
  },
} as const;
