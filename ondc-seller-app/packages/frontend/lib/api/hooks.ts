// React Hooks for Multi-Tenant API Client
'use client';

import { useState, useEffect, useCallback } from 'react';
import { apiClient } from './client';
import {
  TenantId,
  StoreInfoResponse,
  StoreProductsResponse,
  ProductDetailsResponse,
  TenantConfigResponse,
  MultiTenantTestResponse,
  ApiError,
  DEFAULT_CONFIG,
} from './types';

// Generic API Hook State
interface ApiState<T> {
  data: T | null;
  loading: boolean;
  error: ApiError | null;
}

// Custom hook for API state management
function useApiState<T>(): [
  ApiState<T>,
  (data: T | null) => void,
  (loading: boolean) => void,
  (error: ApiError | null) => void
] {
  const [state, setState] = useState<ApiState<T>>({
    data: null,
    loading: false,
    error: null,
  });

  const setData = useCallback((data: T | null) => {
    setState(prev => ({ ...prev, data, error: null }));
  }, []);

  const setLoading = useCallback((loading: boolean) => {
    setState(prev => ({ ...prev, loading }));
  }, []);

  const setError = useCallback((error: ApiError | null) => {
    setState(prev => ({ ...prev, error, loading: false }));
  }, []);

  return [state, setData, setLoading, setError];
}

// Tenant Management Hook
export function useTenant() {
  const [currentTenant, setCurrentTenant] = useState<TenantId>(
    DEFAULT_CONFIG.TENANT_IDS.DEFAULT
  );

  const switchTenant = useCallback(async (tenantId: TenantId) => {
    try {
      setCurrentTenant(tenantId);
      await apiClient.switchTenant(tenantId);
      return true;
    } catch (error) {
      console.error('Failed to switch tenant:', error);
      return false;
    }
  }, []);

  const switchToElectronics = useCallback(() => 
    switchTenant(DEFAULT_CONFIG.TENANT_IDS.ELECTRONICS), [switchTenant]);

  const switchToFashion = useCallback(() => 
    switchTenant(DEFAULT_CONFIG.TENANT_IDS.FASHION), [switchTenant]);

  const switchToDefault = useCallback(() => 
    switchTenant(DEFAULT_CONFIG.TENANT_IDS.DEFAULT), [switchTenant]);

  return {
    currentTenant,
    switchTenant,
    switchToElectronics,
    switchToFashion,
    switchToDefault,
  };
}

// Store Information Hook
export function useStoreInfo() {
  const [state, setData, setLoading, setError] = useApiState<StoreInfoResponse>();

  const fetchStoreInfo = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const data = await apiClient.getStoreInfo();
      setData(data);
    } catch (error) {
      setError(apiClient.isApiError(error) ? error : {
        error: 'Unknown error',
        message: 'Failed to fetch store information',
      });
    } finally {
      setLoading(false);
    }
  }, [setData, setLoading, setError]);

  useEffect(() => {
    fetchStoreInfo();
  }, [fetchStoreInfo]);

  return {
    ...state,
    refetch: fetchStoreInfo,
  };
}

// Store Products Hook
export function useStoreProducts() {
  const [state, setData, setLoading, setError] = useApiState<StoreProductsResponse>();

  const fetchProducts = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const data = await apiClient.getStoreProducts();
      setData(data);
    } catch (error) {
      setError(apiClient.isApiError(error) ? error : {
        error: 'Unknown error',
        message: 'Failed to fetch products',
      });
    } finally {
      setLoading(false);
    }
  }, [setData, setLoading, setError]);

  useEffect(() => {
    fetchProducts();
  }, [fetchProducts]);

  return {
    ...state,
    refetch: fetchProducts,
  };
}

// Product Details Hook
export function useProductDetails(productId: string | null) {
  const [state, setData, setLoading, setError] = useApiState<ProductDetailsResponse>();

  const fetchProductDetails = useCallback(async (id: string) => {
    setLoading(true);
    setError(null);

    try {
      const data = await apiClient.getProductDetails(id);
      setData(data);
    } catch (error) {
      setError(apiClient.isApiError(error) ? error : {
        error: 'Unknown error',
        message: 'Failed to fetch product details',
      });
    } finally {
      setLoading(false);
    }
  }, [setData, setLoading, setError]);

  useEffect(() => {
    if (productId) {
      fetchProductDetails(productId);
    }
  }, [productId, fetchProductDetails]);

  return {
    ...state,
    refetch: productId ? () => fetchProductDetails(productId) : undefined,
  };
}

// Admin Tenant Config Hook
export function useTenantConfig() {
  const [state, setData, setLoading, setError] = useApiState<TenantConfigResponse>();

  const fetchTenantConfig = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const data = await apiClient.getTenantConfig();
      setData(data);
    } catch (error) {
      setError(apiClient.isApiError(error) ? error : {
        error: 'Unknown error',
        message: 'Failed to fetch tenant configuration',
      });
    } finally {
      setLoading(false);
    }
  }, [setData, setLoading, setError]);

  return {
    ...state,
    fetch: fetchTenantConfig,
  };
}

// Multi-Tenant Test Hook
export function useMultiTenantTest() {
  const [state, setData, setLoading, setError] = useApiState<MultiTenantTestResponse>();

  const runTest = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const data = await apiClient.testMultiTenantIsolation();
      setData(data);
    } catch (error) {
      setError(apiClient.isApiError(error) ? error : {
        error: 'Unknown error',
        message: 'Failed to run multi-tenant test',
      });
    } finally {
      setLoading(false);
    }
  }, [setData, setLoading, setError]);

  return {
    ...state,
    runTest,
  };
}

// Authentication Hook
export function useAuth() {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<ApiError | null>(null);

  const login = useCallback(async (email: string, password: string) => {
    setLoading(true);
    setError(null);

    try {
      const response = await apiClient.login(email, password);
      setIsAuthenticated(true);
      return response;
    } catch (error) {
      setError(apiClient.isApiError(error) ? error : {
        error: 'Login failed',
        message: 'Invalid credentials',
      });
      throw error;
    } finally {
      setLoading(false);
    }
  }, []);

  const logout = useCallback(() => {
    apiClient.setAdminToken('');
    setIsAuthenticated(false);
  }, []);

  return {
    isAuthenticated,
    loading,
    error,
    login,
    logout,
  };
}

// Combined hook for tenant-aware data fetching
export function useTenantAwareData() {
  const { currentTenant, switchTenant } = useTenant();
  const storeInfo = useStoreInfo();
  const products = useStoreProducts();

  const refreshData = useCallback(async () => {
    await Promise.all([
      storeInfo.refetch(),
      products.refetch(),
    ]);
  }, [storeInfo.refetch, products.refetch]);

  const switchTenantAndRefresh = useCallback(async (tenantId: TenantId) => {
    const success = await switchTenant(tenantId);
    if (success) {
      await refreshData();
    }
    return success;
  }, [switchTenant, refreshData]);

  return {
    currentTenant,
    storeInfo: storeInfo.data,
    products: products.data,
    loading: storeInfo.loading || products.loading,
    error: storeInfo.error || products.error,
    switchTenant: switchTenantAndRefresh,
    refreshData,
  };
}
