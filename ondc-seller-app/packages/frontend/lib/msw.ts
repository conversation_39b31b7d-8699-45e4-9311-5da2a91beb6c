/**
 * Mock Service Worker (MSW) Setup
 *
 * This module sets up MSW for API mocking during development and testing.
 */

// MSW imports - only for browser environment
let setupWorker: any = null;
let http: any = null;

// Dynamically import MSW only in browser environment
async function initializeMSW() {
  if (typeof window !== 'undefined') {
    try {
      // For MSW v1.x, use the main import
      const msw = await import('msw');
      setupWorker = msw.setupWorker;
      http = msw.rest; // MSW v1.x uses 'rest' instead of 'http'

      return true;
    } catch (error) {
      console.warn('MSW not available:', error);
      return false;
    }
  }
  return false;
}

// Import comprehensive mock data
import {
  mockProducts,
  mockTenants,
  mockCustomers,
  mockTeamMembers,
  mockCoupons,
  mockOrders,
  getProductsByCategory,
  getFilteredProducts,
  getTenantById,
  getCustomerById,
  getReviewsByProductId,
  getProductRating,
  getActiveCoupons,
  getCouponByCode,
} from '../data/msw-data';

// API handlers - create function to get handlers
export const getHandlers = async () => {
  const initialized = await initializeMSW();
  if (!initialized || !http) return [];

  return [
    // Products API
    http.get('/api/products', (req: any, res: any, ctx: any) => {
      const page = parseInt(req.url.searchParams.get('page') || '1');
      const limit = parseInt(req.url.searchParams.get('limit') || '10');
      const category = req.url.searchParams.get('category');
      const search = req.url.searchParams.get('search');

      let filteredProducts = [...mockProducts];

      // Filter by category using the new category field
      if (category) {
        filteredProducts = getProductsByCategory(category);
      }

      // Filter by search
      if (search) {
        filteredProducts = getFilteredProducts({ search });
      }

      // Pagination
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + limit;
      const paginatedProducts = filteredProducts.slice(startIndex, endIndex);

      return res(
        ctx.status(200),
        ctx.json({
          products: paginatedProducts,
          pagination: {
            page,
            limit,
            total: filteredProducts.length,
            totalPages: Math.ceil(filteredProducts.length / limit),
          },
        })
      );
    }),

    // Category-specific products API
    http.get('/api/categories/:slug/products', (req: any, res: any, ctx: any) => {
      const { slug } = req.params;
      const page = parseInt(req.url.searchParams.get('page') || '1');
      const limit = parseInt(req.url.searchParams.get('limit') || '12');
      const sort = req.url.searchParams.get('sort') || 'featured';
      const priceRange = req.url.searchParams.get('priceRange');

      // Map slug to category
      const categoryMap: { [key: string]: string } = {
        electronics: 'electronics',
        fashion: 'fashion',
        'home-garden': 'home-garden',
        'sports-fitness': 'sports-fitness',
        'books-media': 'books-media',
        'beauty-health': 'beauty-health',
      };

      const category = categoryMap[slug];
      if (!category) {
        return res(ctx.status(404), ctx.json({ error: 'Category not found' }));
      }

      let filteredProducts = getProductsByCategory(category);

      // Apply price range filter
      if (priceRange) {
        const [min, max] = priceRange.split('-').map(Number);
        filteredProducts = filteredProducts.filter(
          product => product.price >= min * 100 && product.price <= max * 100
        );
      }

      // Apply sorting
      switch (sort) {
        case 'price-low':
          filteredProducts.sort((a, b) => a.price - b.price);
          break;
        case 'price-high':
          filteredProducts.sort((a, b) => b.price - a.price);
          break;
        case 'rating':
          filteredProducts.sort((a, b) => (b.rating || 0) - (a.rating || 0));
          break;
        case 'newest':
          filteredProducts.sort(
            (a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
          );
          break;
        default: // featured
          filteredProducts.sort((a, b) => (b.reviews || 0) - (a.reviews || 0));
      }

      // Pagination
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + limit;
      const paginatedProducts = filteredProducts.slice(startIndex, endIndex);

      return res(
        ctx.status(200),
        ctx.json({
          products: paginatedProducts,
          pagination: {
            page,
            limit,
            total: filteredProducts.length,
            totalPages: Math.ceil(filteredProducts.length / limit),
          },
          category: {
            slug,
            name: category.charAt(0).toUpperCase() + category.slice(1).replace('-', ' & '),
            productCount: filteredProducts.length,
          },
        })
      );
    }),

    http.get('/api/products/:id', (req: any, res: any, ctx: any) => {
      const { id } = req.params;
      const product = mockProducts.find(p => p.id === id);

      if (!product) {
        return res(ctx.status(404), ctx.json({ error: 'Product not found' }));
      }

      return res(ctx.status(200), ctx.json({ product }));
    }),

    // Store product details endpoint for backend API
    http.get('http://localhost:9000/store/test-products/:id', (req: any, res: any, ctx: any) => {
      const { id } = req.params;
      const tenantId = req.headers.get('x-tenant-id') || 'default';

      // Find product by ID
      const product = mockProducts.find(p => p.id === id);

      if (!product) {
        return res(ctx.status(404), ctx.json({
          error: 'Product not found',
          product_id: id,
          tenant_id: tenantId
        }));
      }

      // Transform mock product to match expected API response
      const transformedProduct = {
        id: product.id,
        title: product.title,
        description: product.description,
        price: {
          amount: product.price,
          currency: 'INR'
        },
        images: product.images || [product.image],
        thumbnail: product.image,
        brand: product.brand || 'Mock Brand',
        rating: product.rating || 4.5,
        review_count: product.reviewCount || 100,
        stock_quantity: product.stock || 50,
        sku: `SKU-${product.id}`,
        category: product.category,
        subcategory: product.subcategory,
        tenant_id: tenantId
      };

      return res(ctx.status(200), ctx.json({
        product: transformedProduct,
        tenant_id: tenantId,
        store: {
          name: 'Mock Store',
          domain: 'mock-store.com'
        },
        api_version: 'v2',
        multi_tenant: true
      }));
    }),

    // Banners API - Removed MSW handler to use real Strapi API

    // Cart API
    http.post('/api/test-cart', (req: any, res: any, ctx: any) => {
      return res(
        ctx.status(200),
        ctx.json({
          cart: {
            id: 'cart_123',
            items: [],
            subtotal: 0,
            total: 0,
            item_count: 0,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          },
        })
      );
    }),

    // Error simulation
    http.get('/api/error-test', (req: any, res: any, ctx: any) => {
      const errorType = req.url.searchParams.get('type');

      switch (errorType) {
        case 'network':
          return res.networkError('Network error');
        case '404':
          return res(ctx.status(404), ctx.json({ error: 'Not found' }));
        case '500':
          return res(ctx.status(500), ctx.json({ error: 'Internal server error' }));
        default:
          return res(ctx.status(200), ctx.json({ message: 'No error' }));
      }
    }),

    // Strapi API mocks
    // Banners handler removed - using real Strapi API

    http.get(
      `${process.env.NEXT_PUBLIC_STRAPI_API_URL}/api/products`,
      (req: any, res: any, ctx: any) => {
        return res(
          ctx.status(200),
          ctx.json({
            data: mockProducts.map(product => ({
              id: product.id,
              attributes: {
                name: product.title,
                description: product.description,
                price: product.price,
                sku: `SKU-${product.id}`,
                category: product.product_type,
                isActive: true,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
              },
            })),
            meta: {
              pagination: {
                page: 1,
                pageSize: 25,
                pageCount: 1,
                total: mockProducts.length,
              },
            },
          })
        );
      }
    ),

    // Enhanced API endpoints for new functionality

    // Reviews endpoints
    http.get('/api/reviews/:productId', (req: any, res: any, ctx: any) => {
      const { productId } = req.params;
      const reviews = getReviewsByProductId(productId);
      return res(ctx.status(200), ctx.json({ reviews }));
    }),

    http.get('/api/products/:productId/rating', (req: any, res: any, ctx: any) => {
      const { productId } = req.params;
      const rating = getProductRating(productId);
      return res(ctx.status(200), ctx.json(rating));
    }),

    // Tenants endpoints
    http.get('/api/tenants', (req: any, res: any, ctx: any) => {
      return res(ctx.status(200), ctx.json({ tenants: mockTenants }));
    }),

    http.get('/api/tenants/:id', (req: any, res: any, ctx: any) => {
      const { id } = req.params;
      const tenant = getTenantById(id);
      if (!tenant) {
        return res(ctx.status(404), ctx.json({ error: 'Tenant not found' }));
      }
      return res(ctx.status(200), ctx.json({ tenant }));
    }),

    // Customers endpoints
    http.get('/api/customers', (req: any, res: any, ctx: any) => {
      return res(ctx.status(200), ctx.json({ customers: mockCustomers }));
    }),

    http.get('/api/customers/:id', (req: any, res: any, ctx: any) => {
      const { id } = req.params;
      const customer = getCustomerById(id);
      if (!customer) {
        return res(ctx.status(404), ctx.json({ error: 'Customer not found' }));
      }
      return res(ctx.status(200), ctx.json({ customer }));
    }),

    // Team members endpoints
    http.get('/api/team', (req: any, res: any, ctx: any) => {
      return res(ctx.status(200), ctx.json({ members: mockTeamMembers }));
    }),

    // Coupons endpoints
    http.get('/api/coupons', (req: any, res: any, ctx: any) => {
      return res(ctx.status(200), ctx.json({ coupons: mockCoupons }));
    }),

    http.get('/api/coupons/active', (req: any, res: any, ctx: any) => {
      const activeCoupons = getActiveCoupons();
      return res(ctx.status(200), ctx.json({ coupons: activeCoupons }));
    }),

    http.post('/api/coupons/validate', (req: any, res: any, ctx: any) => {
      const { code } = req.body;
      const coupon = getCouponByCode(code);
      if (!coupon || !coupon.is_active) {
        return res(ctx.status(404), ctx.json({ error: 'Invalid coupon code' }));
      }
      return res(ctx.status(200), ctx.json({ coupon, valid: true }));
    }),

    // Orders endpoints with enhanced data
    http.get('/api/orders', (req: any, res: any, ctx: any) => {
      return res(ctx.status(200), ctx.json({ orders: mockOrders }));
    }),

    http.get('/api/orders/:id', (req: any, res: any, ctx: any) => {
      const { id } = req.params;
      const order = mockOrders.find(o => o.id === id);
      if (!order) {
        return res(ctx.status(404), ctx.json({ error: 'Order not found' }));
      }
      return res(ctx.status(200), ctx.json({ order }));
    }),
  ];
};

// Setup for browser
export const createWorker = async () => {
  const initialized = await initializeMSW();
  if (!initialized || !setupWorker) return null;

  const handlers = await getHandlers();
  if (handlers.length === 0) return null;

  return setupWorker(...handlers);
};

// Start MSW
export async function startMSW() {
  if (typeof window !== 'undefined') {
    try {
      const worker = await createWorker();
      if (worker) {
        await worker.start({
          onUnhandledRequest: 'bypass',
        });
        console.log('🔶 MSW started in browser');
        return worker;
      }
    } catch (error) {
      console.warn('Failed to start MSW:', error);
    }
  }
  return null;
}

// Stop MSW
export async function stopMSW() {
  if (typeof window !== 'undefined') {
    try {
      const worker = await createWorker();
      if (worker) {
        worker.stop();
        console.log('🔶 MSW stopped');
      }
    } catch (error) {
      console.warn('Failed to stop MSW:', error);
    }
  }
}

// Enable/disable MSW based on environment - CRITICAL: Only enable when explicitly set to true
export const isMSWEnabled = () => {
  if (typeof process === 'undefined') return false;
  // CRITICAL FIX: Only enable MSW when explicitly set to true, not by default in development
  return process.env.NEXT_PUBLIC_MSW_ENABLED === 'true';
};

// Export worker for compatibility
export const worker = {
  start: startMSW,
  stop: stopMSW,
};

// Additional utility functions for compatibility
export function isMSWEnabledInStorage(): boolean {
  if (typeof window === 'undefined') return false;

  try {
    return localStorage.getItem('msw-enabled') === 'true';
  } catch {
    return false;
  }
}
