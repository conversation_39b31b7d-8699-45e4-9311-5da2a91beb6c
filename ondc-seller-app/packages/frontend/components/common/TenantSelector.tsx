'use client';

import React, { useState } from 'react';
import { ChevronDownIcon, BuildingStorefrontIcon } from '@heroicons/react/24/outline';
import { useTenant } from '@/contexts/TenantContext';
import { safeExtractText } from '@/lib/utils/richTextParser';

export default function TenantSelector() {
  const { selectedTenant, setSelectedTenant, sellers = [], loading } = useTenant();
  const [isOpen, setIsOpen] = useState(false);

  if (loading) {
    return (
      <div className="relative">
        <div className="flex items-center space-x-2 px-3 py-2 bg-gray-100 rounded-lg animate-pulse">
          <BuildingStorefrontIcon className="h-5 w-5 text-gray-400" />
          <div className="h-4 bg-gray-300 rounded w-24"></div>
        </div>
      </div>
    );
  }

  if (sellers.length === 0) {
    return null;
  }

  return (
    <div className="relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-2 px-3 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
      >
        <BuildingStorefrontIcon className="h-5 w-5 text-gray-600" />
        <span className="text-sm font-medium text-gray-900">
          {selectedTenant?.name || 'Select Tenant'}
        </span>
        <ChevronDownIcon 
          className={`h-4 w-4 text-gray-600 transition-transform ${
            isOpen ? 'rotate-180' : ''
          }`} 
        />
      </button>

      {isOpen && (
        <>
          {/* Backdrop */}
          <div 
            className="fixed inset-0 z-10" 
            onClick={() => setIsOpen(false)}
          />
          
          {/* Dropdown */}
          <div className="absolute top-full left-0 mt-1 w-80 bg-white border border-gray-200 rounded-lg shadow-lg z-20 max-h-96 overflow-y-auto">
            <div className="p-2">
              <div className="text-xs font-medium text-gray-500 uppercase tracking-wide px-3 py-2">
                Select Tenant/Seller
              </div>
              {sellers.map((seller) => (
                <button
                  key={seller.id}
                  onClick={() => {
                    setSelectedTenant(seller);
                    setIsOpen(false);
                  }}
                  className={`w-full text-left px-3 py-3 rounded-md hover:bg-gray-50 transition-colors ${
                    selectedTenant?.id === seller.id 
                      ? 'bg-blue-50 border-l-4 border-blue-500' 
                      : ''
                  }`}
                >
                  <div className="flex items-center space-x-3">
                    <div className="flex-shrink-0">
                      <div className="h-10 w-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                        <span className="text-white font-semibold text-sm">
                          {seller.name.charAt(0).toUpperCase()}
                        </span>
                      </div>
                    </div>
                    <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 truncate">
                          {seller.name}
                        </p>
                    </div>
                  </div>
                </button>
              ))}
            </div>
          </div>
        </>
      )}
    </div>
  );
}
