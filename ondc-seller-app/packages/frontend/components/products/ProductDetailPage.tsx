'use client';

import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import {
  Box,
  Container,
  Grid,
  Typography,
  Button,
  Card,
  CardMedia,
  Chip,
  Rating,
  Alert,
  CircularProgress,
  Tabs,
  Tab,
  List,
  ListItem,
  ListItemText,
  Breadcrumbs,
  Link,
  IconButton
} from '@mui/material';
import {
  Add as AddIcon,
  Remove as RemoveIcon,
  ShoppingCart as CartIcon,
  Favorite as FavoriteIcon,
  FavoriteBorder as FavoriteBorderIcon,
  Share as ShareIcon,
  Home as HomeIcon
} from '@mui/icons-material';

import { apiClient } from '../../lib/api/client';
import { useCart } from '../../hooks/useCart';
import { useTenant } from '../../hooks/useTenant';

/* ------------------------------------------------------------------ */
/*  Types                                                              */
/* ------------------------------------------------------------------ */
interface Price {
  amount: number;
  currency: string;
}

interface ShippingInfo {
  free_shipping: boolean;
  estimated_delivery: string;
  regions: string[];
}

interface StoreInfo {
  id: string;
  name: string;
  domain: string;
}

interface Product {
  id: string;
  title: string;
  description: string;
  handle: string;
  status: string;
  thumbnail: string | null;
  material?: string | null;
  origin_country?: string | null;
  availability: 'in_stock' | 'out_of_stock' | string;
  price: Price;
  shipping?: ShippingInfo;
  warranty?: string;
  /* OPTIONAL / LEGACY FIELDS (may be absent) ----------------------- */
  images?: string[];
  brand?: string;
  review_count?: number;
  rating?: number;
  specifications?: Record<string, string>;
  features?: string[];
  stock_quantity?: number;
  sku?: string;
  category?: string;
  subcategory?: string;
  /* ---------------------------------------------------------------- */
  store?: StoreInfo;
  tenant_id: string;
}

/* ------------------------------------------------------------------ */
/*  Utilities                                                          */
/* ------------------------------------------------------------------ */
const formatPrice = (p: Price | undefined) =>
  p ? `₹${p.amount.toLocaleString()}` : '—';

/* ------------------------------------------------------------------ */
/*  Component                                                          */
/* ------------------------------------------------------------------ */
export default function ProductDetailPage() {
  const params = useParams();
  const router = useRouter();
  const { currentTenant } = useTenant();
  const { addToCart, isLoading: cartLoading } = useCart();

  const [product, setProduct] = useState<Product | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [quantity, setQuantity] = useState(1);
  const [selectedImage, setSelectedImage] = useState(0);
  const [isFavorite, setIsFavorite] = useState(false);
  const [tabValue, setTabValue] = useState(0);
  const [addedToCart, setAddedToCart] = useState(false);

  const productId = params?.id as string | undefined;

  /* -------------------------------------------------------------- */
  /*  Data fetch                                                    */
  /* -------------------------------------------------------------- */
  useEffect(() => {
    if (!productId || !currentTenant) return;

    (async () => {
      try {
        setLoading(true);
        setError(null);

        const res = await apiClient.getProductDetails(productId);
        if (res?.product) {
          setProduct(res.product as Product);
        } else {
          setError('Product not found');
        }
      } catch (err) {
        console.error(err);
        setError('Failed to load product details');
      } finally {
        setLoading(false);
      }
    })();
  }, [productId, currentTenant]);

  /* -------------------------------------------------------------- */
  /*  Handlers                                                      */
  /* -------------------------------------------------------------- */
  const changeQty = (delta: number) =>
    setQuantity((q) => Math.max(1, q + delta));

  const handleAddToCart = async () => {
    if (!product) return;
    try {
      await addToCart({
        product_id: product.id,
        quantity,
        price: product.price.amount
      });
      setAddedToCart(true);
      setTimeout(() => setAddedToCart(false), 3000);
    } catch (e) {
      console.error(e);
    }
  };

  const handleTabChange = (_: React.SyntheticEvent, v: number) => setTabValue(v);

  /* -------------------------------------------------------------- */
  /*  Loading / error states                                        */
  /* -------------------------------------------------------------- */
  if (loading) {
    return (
      <Container maxWidth="lg" sx={{ py: 4, textAlign: 'center' }}>
        <CircularProgress />
        <Typography variant="h6" sx={{ mt: 2 }}>
          Loading product details…
        </Typography>
      </Container>
    );
  }

  if (error || !product) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Alert severity="error" sx={{ mb: 2 }}>
          {error || 'Product not found'}
        </Alert>
        <Button variant="contained" onClick={() => router.back()}>
          Go Back
        </Button>
      </Container>
    );
  }

  /* -------------------------------------------------------------- */
  /*  Derived data                                                  */
  /* -------------------------------------------------------------- */
  const {
    title,
    description,
    images = [],
    thumbnail,
    brand,
    rating = 0,
    review_count = 0,
    specifications = {},
    features = [],
    availability,
    stock_quantity = 1,
    sku
  } = product;

  const gallery = images.length
    ? images
    : thumbnail
    ? [thumbnail]
    : ['/images/placeholder.jpg'];

  const inStock = availability === 'in_stock';

  /* -------------------------------------------------------------- */
  /*  Render                                                        */
  /* -------------------------------------------------------------- */
  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* ---------------------------------------------------------- */}
      {/*  Breadcrumbs                                              */}
      {/* ---------------------------------------------------------- */}
      <Breadcrumbs aria-label="breadcrumb" sx={{ mb: 3 }}>
        <Link color="inherit" href="/" sx={{ display: 'flex', alignItems: 'center' }}>
          <HomeIcon sx={{ mr: 0.5 }} fontSize="inherit" />
          Home
        </Link>
        {product.category && (
          <Link color="inherit" href={`/categories/${product.category}`}>
            {product.category}
          </Link>
        )}
        {product.subcategory && (
          <Link
            color="inherit"
            href={`/categories/${product.category}?subcategory=${product.subcategory}`}
          >
            {product.subcategory}
          </Link>
        )}
        <Typography color="text.primary">{title}</Typography>
      </Breadcrumbs>

      <Grid container spacing={4}>
        {/* -------------------------------------------------------- */}
        {/*  IMAGES                                                 */}
        {/* -------------------------------------------------------- */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardMedia
              component="img"
              height="400"
              image={gallery[selectedImage]}
              alt={title}
              sx={{ objectFit: 'contain' }}
            />
          </Card>

          {gallery.length > 1 && (
            <Box sx={{ display: 'flex', gap: 1, mt: 2, overflowX: 'auto' }}>
              {gallery.map((img, i) => (
                <Box
                  key={i}
                  component="img"
                  src={img}
                  alt={`${title} ${i + 1}`}
                  onClick={() => setSelectedImage(i)}
                  sx={{
                    width: 80,
                    height: 80,
                    objectFit: 'cover',
                    border:
                      selectedImage === i
                        ? '2px solid'
                        : '1px solid',
                    borderColor:
                      selectedImage === i ? 'primary.main' : 'grey.300',
                    borderRadius: 1,
                    cursor: 'pointer',
                    flexShrink: 0
                  }}
                />
              ))}
            </Box>
          )}
        </Grid>

        {/* -------------------------------------------------------- */}
        {/*  DETAILS                                                */}
        {/* -------------------------------------------------------- */}
        <Grid item xs={12} md={6}>
          {/* Brand */}
          {brand && (
            <Typography variant="subtitle2" color="text.secondary" gutterBottom>
              {brand}
            </Typography>
          )}

          {/* Title */}
          <Typography variant="h4" component="h1" gutterBottom>
            {title}
          </Typography>

          {/* Rating */}
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <Rating value={rating} readOnly precision={0.1} />
            <Typography variant="body2" sx={{ ml: 1 }}>
              ({review_count} reviews)
            </Typography>
          </Box>

          {/* Price */}
          <Typography
            variant="h5"
            component="span"
            color="primary"
            fontWeight="bold"
            sx={{ mb: 3, display: 'block' }}
          >
            {formatPrice(product.price)}
          </Typography>

          {/* Stock */}
          <Chip
            label={
              inStock
                ? `In Stock${stock_quantity ? ` (${stock_quantity})` : ''}`
                : 'Out of Stock'
            }
            color={inStock ? 'success' : 'error'}
            variant="outlined"
            sx={{ mb: 3 }}
          />

          {/* Quantity selector */}
          {inStock && (
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
              <Typography variant="body1" sx={{ mr: 2 }}>
                Quantity:
              </Typography>
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  border: '1px solid',
                  borderColor: 'grey.300',
                  borderRadius: 1
                }}
              >
                <IconButton
                  size="small"
                  onClick={() => changeQty(-1)}
                  disabled={quantity <= 1}
                >
                  <RemoveIcon />
                </IconButton>
                <Typography sx={{ px: 2, minWidth: 40, textAlign: 'center' }}>
                  {quantity}
                </Typography>
                <IconButton
                  size="small"
                  onClick={() => changeQty(1)}
                  disabled={quantity >= stock_quantity}
                >
                  <AddIcon />
                </IconButton>
              </Box>
            </Box>
          )}

          {/* Action buttons */}
          <Box sx={{ display: 'flex', gap: 2, mb: 3 }}>
            <Button
              variant="contained"
              size="large"
              startIcon={<CartIcon />}
              onClick={handleAddToCart}
              disabled={!inStock || cartLoading || addedToCart}
              sx={{ flex: 1 }}
            >
              {addedToCart ? 'Added' : 'Add to Cart'}
            </Button>
            <IconButton
              onClick={() => setIsFavorite(!isFavorite)}
              color={isFavorite ? 'error' : 'default'}
              size="large"
            >
              {isFavorite ? <FavoriteIcon /> : <FavoriteBorderIcon />}
            </IconButton>
            <IconButton size="large">
              <ShareIcon />
            </IconButton>
          </Box>

          {/* SKU */}
          {sku && (
            <Typography variant="body2" color="text.secondary">
              SKU: {sku}
            </Typography>
          )}
        </Grid>
      </Grid>

      {/* ---------------------------------------------------------- */}
      {/*  Tabs                                                     */}
      {/* ---------------------------------------------------------- */}
      <Box sx={{ mt: 6 }}>
        <Tabs value={tabValue} onChange={handleTabChange}>
          <Tab label="Description" />
          <Tab label="Specifications" />
          <Tab label="Features" />
        </Tabs>

        {/* Description */}
        {tabValue === 0 && (
          <Box sx={{ p: 3 }}>
            <Typography variant="body1" paragraph>
              {description}
            </Typography>
            {product.warranty && (
              <Typography variant="body2" color="text.secondary">
                Warranty: {product.warranty}
              </Typography>
            )}
            {product.shipping && (
              <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                Delivery: {product.shipping.estimated_delivery}{' '}
                {product.shipping.free_shipping && '(Free)'}
              </Typography>
            )}
          </Box>
        )}

        {/* Specifications */}
        {tabValue === 1 && (
          <Box sx={{ p: 3 }}>
            {Object.keys(specifications).length ? (
              <List>
                {Object.entries(specifications).map(([k, v]) => (
                  <ListItem key={k} divider>
                    <ListItemText
                      primary={k}
                      secondary={v}
                      primaryTypographyProps={{ fontWeight: 'medium' }}
                    />
                  </ListItem>
                ))}
              </List>
            ) : (
              <Typography variant="body2">No specifications provided.</Typography>
            )}
          </Box>
        )}

        {/* Features */}
        {tabValue === 2 && (
          <Box sx={{ p: 3 }}>
            {features.length ? (
              <List>
                {features.map((f, i) => (
                  <ListItem key={i}>
                    <ListItemText primary={`• ${f}`} />
                  </ListItem>
                ))}
              </List>
            ) : (
              <Typography variant="body2">No features listed.</Typography>
            )}
          </Box>
        )}
      </Box>
    </Container>
  );
}
